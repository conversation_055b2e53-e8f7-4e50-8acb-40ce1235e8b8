class FoodEntry {
  final int? id;
  final String food;
  final int calories;
  final DateTime time;

  FoodEntry({
    this.id,
    required this.food,
    required this.calories,
    required this.time,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'food': food,
      'calories': calories,
      'time': time.millisecondsSinceEpoch,
    };
  }

  factory FoodEntry.fromMap(Map<String, dynamic> map) {
    return FoodEntry(
      id: map['id'],
      food: map['food'],
      calories: map['calories'],
      time: DateTime.fromMillisecondsSinceEpoch(map['time']),
    );
  }

  FoodEntry copyWith({
    int? id,
    String? food,
    int? calories,
    DateTime? time,
  }) {
    return FoodEntry(
      id: id ?? this.id,
      food: food ?? this.food,
      calories: calories ?? this.calories,
      time: time ?? this.time,
    );
  }
}
