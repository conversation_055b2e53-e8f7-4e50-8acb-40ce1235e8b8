import 'package:flutter/material.dart';
import '../models/exercise.dart';
import '../models/exercise_log.dart';
import '../models/exercise_set.dart';
import '../services/database_service.dart';

class ExerciseLoggingScreen extends StatefulWidget {
  final Exercise exercise;
  final ExerciseLog? existingLog;

  const ExerciseLoggingScreen({
    super.key,
    required this.exercise,
    this.existingLog,
  });

  @override
  State<ExerciseLoggingScreen> createState() => _ExerciseLoggingScreenState();
}

class _ExerciseLoggingScreenState extends State<ExerciseLoggingScreen> {
  late ExerciseLog _exerciseLog;
  List<ExerciseSet> _sets = [];
  final DatabaseService _databaseService = DatabaseService();
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _initializeLog();
  }

  void _initializeLog() {
    if (widget.existingLog != null) {
      _exerciseLog = widget.existingLog!;
      _loadExistingSets();
    } else {
      _exerciseLog = ExerciseLog(
        workoutSessionId: 0, // This should be passed from the session
        exerciseId: widget.exercise.id!,
        exercise: widget.exercise,
        startTime: DateTime.now(),
      );
      _addInitialSet();
    }
  }

  Future<void> _loadExistingSets() async {
    if (_exerciseLog.id != null) {
      setState(() {
        _isLoading = true;
      });
      
      try {
        final sets = await _databaseService.getExerciseSetsForLog(_exerciseLog.id!);
        setState(() {
          _sets = sets;
          _isLoading = false;
        });
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _addInitialSet() {
    _sets.add(ExerciseSet(
      exerciseLogId: _exerciseLog.id ?? 0,
      setNumber: 1,
      reps: widget.exercise.defaultReps,
      weight: widget.exercise.defaultWeight,
      timestamp: DateTime.now(),
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.exercise.name),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveExerciseLog,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Exercise info card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            widget.exercise.name,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            widget.exercise.description,
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          const SizedBox(height: 8),
                          Row(
                            children: [
                              Chip(
                                label: Text(widget.exercise.muscleGroupsDisplay),
                                backgroundColor: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                              ),
                              const SizedBox(width: 8),
                              Chip(
                                label: Text(widget.exercise.equipmentDisplay),
                                backgroundColor: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Sets header
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Sets',
                        style: Theme.of(context).textTheme.titleLarge,
                      ),
                      TextButton.icon(
                        onPressed: _addSet,
                        icon: const Icon(Icons.add),
                        label: const Text('Add Set'),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  
                  // Sets list
                  Expanded(
                    child: ListView.builder(
                      itemCount: _sets.length,
                      itemBuilder: (context, index) {
                        return _buildSetCard(index);
                      },
                    ),
                  ),
                  
                  // Summary
                  if (_sets.isNotEmpty) ...[
                    const Divider(),
                    _buildSummary(),
                  ],
                ],
              ),
            ),
    );
  }

  Widget _buildSetCard(int index) {
    final set = _sets[index];
    final isCardio = widget.exercise.type == ExerciseType.cardio;
    
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Row(
              children: [
                // Set number
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: set.completed 
                        ? Theme.of(context).colorScheme.primary 
                        : Colors.grey[300],
                    shape: BoxShape.circle,
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: TextStyle(
                        color: set.completed ? Colors.white : Colors.black,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                
                // Input fields
                Expanded(
                  child: Row(
                    children: [
                      if (!isCardio) ...[
                        // Reps
                        Expanded(
                          child: TextFormField(
                            initialValue: set.reps.toString(),
                            decoration: const InputDecoration(
                              labelText: 'Reps',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) {
                              final reps = int.tryParse(value) ?? 0;
                              _updateSet(index, set.copyWith(reps: reps));
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        
                        // Weight
                        Expanded(
                          child: TextFormField(
                            initialValue: set.weight.toString(),
                            decoration: const InputDecoration(
                              labelText: 'Weight (kg)',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) {
                              final weight = double.tryParse(value) ?? 0.0;
                              _updateSet(index, set.copyWith(weight: weight));
                            },
                          ),
                        ),
                      ] else ...[
                        // Duration for cardio
                        Expanded(
                          child: TextFormField(
                            initialValue: (set.duration ~/ 60).toString(),
                            decoration: const InputDecoration(
                              labelText: 'Minutes',
                              border: OutlineInputBorder(),
                              contentPadding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) {
                              final minutes = int.tryParse(value) ?? 0;
                              _updateSet(index, set.copyWith(duration: minutes * 60));
                            },
                          ),
                        ),
                      ],
                      const SizedBox(width: 8),
                      
                      // Complete button
                      IconButton(
                        onPressed: () {
                          _updateSet(index, set.copyWith(completed: !set.completed));
                        },
                        icon: Icon(
                          set.completed ? Icons.check_circle : Icons.radio_button_unchecked,
                          color: set.completed ? Colors.green : Colors.grey,
                        ),
                      ),
                      
                      // Delete button
                      IconButton(
                        onPressed: () => _removeSet(index),
                        icon: const Icon(Icons.delete, color: Colors.red),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            // Rest timer (if not last set)
            if (index < _sets.length - 1 && set.completed) ...[
              const SizedBox(height: 8),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'Rest: ${set.formattedRestTime}',
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.secondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSummary() {
    final completedSets = _sets.where((set) => set.completed).length;
    final totalVolume = _sets.fold(0.0, (sum, set) => sum + set.volume);
    final totalReps = _sets.fold(0, (sum, set) => sum + set.reps);
    
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Summary',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildSummaryItem('Sets', '$completedSets/${_sets.length}'),
              if (widget.exercise.type != ExerciseType.cardio) ...[
                _buildSummaryItem('Total Reps', totalReps.toString()),
                _buildSummaryItem('Volume', '${totalVolume.toStringAsFixed(1)}kg'),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  void _addSet() {
    setState(() {
      _sets.add(ExerciseSet(
        exerciseLogId: _exerciseLog.id ?? 0,
        setNumber: _sets.length + 1,
        reps: widget.exercise.defaultReps,
        weight: widget.exercise.defaultWeight,
        timestamp: DateTime.now(),
      ));
    });
  }

  void _removeSet(int index) {
    setState(() {
      _sets.removeAt(index);
      // Update set numbers
      for (int i = 0; i < _sets.length; i++) {
        _sets[i] = _sets[i].copyWith(setNumber: i + 1);
      }
    });
  }

  void _updateSet(int index, ExerciseSet updatedSet) {
    setState(() {
      _sets[index] = updatedSet;
    });
  }

  Future<void> _saveExerciseLog() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Save exercise log
      int logId;
      if (_exerciseLog.id == null) {
        logId = await _databaseService.insertExerciseLog(_exerciseLog);
        _exerciseLog = _exerciseLog.copyWith(id: logId);
      } else {
        logId = _exerciseLog.id!;
      }

      // Save all sets
      for (final set in _sets) {
        final setWithLogId = set.copyWith(exerciseLogId: logId);
        if (set.id == null) {
          await _databaseService.insertExerciseSet(setWithLogId);
        } else {
          await _databaseService.updateExerciseSet(setWithLogId);
        }
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Exercise saved successfully! 💪'),
            backgroundColor: Colors.green,
          ),
        );
        Navigator.of(context).pop();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving exercise: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
