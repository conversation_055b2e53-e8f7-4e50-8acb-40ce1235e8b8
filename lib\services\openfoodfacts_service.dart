import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/product.dart';

class OpenFoodFactsService {
  static const String _baseUrl = 'https://world.openfoodfacts.org/api/v0/product';
  
  /// Fetches product information from OpenFoodFacts API
  /// Uses Dutch language preference for better results in Netherlands
  Future<Product?> getProductByBarcode(String barcode) async {
    try {
      final url = Uri.parse('$_baseUrl/$barcode.json');
      
      final response = await http.get(
        url,
        headers: {
          'User-Agent': 'NonniHealthJourney/1.0.0 (https://github.com/yourapp)',
          'Accept-Language': 'nl,en;q=0.9', // Prefer Dutch, fallback to English
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        
        // Check if product was found
        if (data['status'] == 1 && data['product'] != null) {
          return Product.fromOpenFoodFacts(data, barcode);
        } else {
          print('Product not found for barcode: $barcode');
          return null;
        }
      } else {
        print('HTTP Error: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Error fetching product data: $e');
      return null;
    }
  }

  /// Searches for products by name (useful for manual search)
  Future<List<Product>> searchProducts(String query) async {
    try {
      final url = Uri.parse('https://world.openfoodfacts.org/cgi/search.pl')
          .replace(queryParameters: {
        'search_terms': query,
        'search_simple': '1',
        'action': 'process',
        'json': '1',
        'page_size': '20',
        'fields': 'code,product_name,brands,nutriments,image_url,serving_size',
      });

      final response = await http.get(
        url,
        headers: {
          'User-Agent': 'NonniHealthJourney/1.0.0 (https://github.com/yourapp)',
          'Accept-Language': 'nl,en;q=0.9',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final products = data['products'] as List<dynamic>? ?? [];
        
        return products
            .where((product) => product['code'] != null)
            .map((product) => Product.fromOpenFoodFacts(
                {'product': product}, 
                product['code'].toString()
            ))
            .toList();
      } else {
        print('Search HTTP Error: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      print('Error searching products: $e');
      return [];
    }
  }

  /// Gets popular products in Netherlands (for suggestions)
  Future<List<Product>> getPopularDutchProducts() async {
    try {
      final url = Uri.parse('https://nl.openfoodfacts.org/cgi/search.pl')
          .replace(queryParameters: {
        'action': 'process',
        'json': '1',
        'page_size': '50',
        'sort_by': 'popularity',
        'fields': 'code,product_name,brands,nutriments,image_url,serving_size',
      });

      final response = await http.get(
        url,
        headers: {
          'User-Agent': 'NonniHealthJourney/1.0.0 (https://github.com/yourapp)',
          'Accept-Language': 'nl,en;q=0.9',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final products = data['products'] as List<dynamic>? ?? [];
        
        return products
            .where((product) => 
                product['code'] != null && 
                product['nutriments'] != null &&
                product['nutriments']['energy-kcal_100g'] != null
            )
            .take(20) // Limit to top 20
            .map((product) => Product.fromOpenFoodFacts(
                {'product': product}, 
                product['code'].toString()
            ))
            .toList();
      } else {
        print('Popular products HTTP Error: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      print('Error fetching popular products: $e');
      return [];
    }
  }
}
