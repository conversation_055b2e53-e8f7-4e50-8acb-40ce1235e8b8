enum ProgramStatus {
  active,
  paused,
  completed,
  cancelled,
}

class UserActiveProgram {
  final int? id;
  final int programId;
  final DateTime startDate;
  final int currentWeek;
  final ProgramStatus status;
  final String notes;

  UserActiveProgram({
    this.id,
    required this.programId,
    required this.startDate,
    this.currentWeek = 1,
    this.status = ProgramStatus.active,
    this.notes = '',
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'program_id': programId,
      'start_date': startDate.millisecondsSinceEpoch,
      'current_week': currentWeek,
      'status': status.name,
      'notes': notes,
    };
  }

  factory UserActiveProgram.fromMap(Map<String, dynamic> map) {
    return UserActiveProgram(
      id: map['id'],
      programId: map['program_id'],
      startDate: DateTime.fromMillisecondsSinceEpoch(map['start_date']),
      currentWeek: map['current_week'] ?? 1,
      status: ProgramStatus.values.firstWhere(
        (e) => e.name == map['status'],
        orElse: () => ProgramStatus.active,
      ),
      notes: map['notes'] ?? '',
    );
  }

  String get statusDisplay {
    switch (status) {
      case ProgramStatus.active:
        return 'Active';
      case ProgramStatus.paused:
        return 'Paused';
      case ProgramStatus.completed:
        return 'Completed';
      case ProgramStatus.cancelled:
        return 'Cancelled';
    }
  }

  int get daysActive {
    return DateTime.now().difference(startDate).inDays;
  }

  double getProgressPercentage(int totalWeeks) {
    if (status == ProgramStatus.completed) return 1.0;
    return (currentWeek - 1) / totalWeeks;
  }

  UserActiveProgram copyWith({
    int? id,
    int? programId,
    DateTime? startDate,
    int? currentWeek,
    ProgramStatus? status,
    String? notes,
  }) {
    return UserActiveProgram(
      id: id ?? this.id,
      programId: programId ?? this.programId,
      startDate: startDate ?? this.startDate,
      currentWeek: currentWeek ?? this.currentWeek,
      status: status ?? this.status,
      notes: notes ?? this.notes,
    );
  }
}
