import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/workout_entry.dart';
import '../models/workout_session.dart';
import '../models/exercise.dart';
import '../services/database_service.dart';
import 'workout_session_screen.dart';
import 'exercise_logging_screen.dart';

class TrainingProgressScreen extends StatefulWidget {
  const TrainingProgressScreen({super.key});

  @override
  State<TrainingProgressScreen> createState() => _TrainingProgressScreenState();
}

class _TrainingProgressScreenState extends State<TrainingProgressScreen> {
  List<WorkoutEntry> _workoutEntries = [];
  List<WorkoutSession> _workoutSessions = [];
  final DatabaseService _databaseService = DatabaseService();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadWorkouts();
  }

  Future<void> _loadWorkouts() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final workouts = await _databaseService.getAllWorkoutEntries();
      final sessions = await _databaseService.getRecentWorkoutSessions(limit: 20);

      setState(() {
        _workoutEntries = workouts;
        _workoutSessions = sessions;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _addWorkoutEntry() async {
    showDialog(
      context: context,
      builder: (context) => _AddWorkoutDialog(
        onAdd: (String exercise, int duration, String notes) async {
          final entry = WorkoutEntry(
            exercise: exercise,
            duration: duration,
            notes: notes,
            date: DateTime.now(),
          );

          await _databaseService.insertWorkoutEntry(entry);
          await _loadWorkouts(); // Reload data to update UI
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Calculate weekly stats
    final now = DateTime.now();
    final weekStart = now.subtract(Duration(days: now.weekday - 1));
    final weeklyWorkouts = _workoutEntries.where((entry) {
      return entry.date.isAfter(weekStart);
    }).toList();

    final totalWeeklyMinutes = weeklyWorkouts.fold<int>(
      0, (sum, entry) => sum + entry.duration
    );

    return Scaffold(
      appBar: AppBar(
        title: const Text("Nonni's Fitness Journey"),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Weekly Summary Card
            Container(
              margin: const EdgeInsets.all(16),
              child: Card(
                elevation: 6,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                        Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.1),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.emoji_events,
                              color: Theme.of(context).colorScheme.tertiary,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              "Nonni's Weekly Progress",
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildStatInfo('Workouts', weeklyWorkouts.length.toString(), Theme.of(context).colorScheme.primary),
                            _buildStatInfo('Minutes', totalWeeklyMinutes.toString(), Theme.of(context).colorScheme.secondary),
                            _buildStatInfo('Avg/Day',
                              (totalWeeklyMinutes / 7).round().toString(), Theme.of(context).colorScheme.tertiary),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Progress Analytics
            if (_workoutSessions.isNotEmpty) ...[
              Card(
                elevation: 6,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                        Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.trending_up,
                              color: Theme.of(context).colorScheme.secondary,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              "Nonni's Progress Analytics",
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildAnalyticsItem(
                              'Completed Sessions',
                              _workoutSessions.where((s) => s.status == SessionStatus.completed).length.toString(),
                              Theme.of(context).colorScheme.primary,
                            ),
                            _buildAnalyticsItem(
                              'Total Volume',
                              '${_workoutSessions.fold(0.0, (sum, s) => sum + s.totalVolume).toStringAsFixed(0)}kg',
                              Theme.of(context).colorScheme.secondary,
                            ),
                            _buildAnalyticsItem(
                              'Avg Duration',
                              _getAverageDuration(),
                              Theme.of(context).colorScheme.tertiary,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Workout History
            Expanded(
              child: _workoutEntries.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.fitness_center,
                            size: 80,
                            color: Colors.grey[300],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Time to get moving, Nonni! 💪',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Your fitness journey starts here!\nTap "Log Workout" to record your first exercise session.',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _workoutEntries.length,
                      itemBuilder: (context, index) {
                        final entry = _workoutEntries[index];
                        return Card(
                          elevation: 2,
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                          child: ListTile(
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.fitness_center,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                            title: Text(
                              entry.exercise,
                              style: const TextStyle(fontWeight: FontWeight.w600),
                            ),
                            subtitle: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  DateFormat('MMM dd, yyyy - HH:mm').format(entry.date),
                                  style: TextStyle(color: Colors.grey[600]),
                                ),
                                if (entry.notes.isNotEmpty)
                                  Padding(
                                    padding: const EdgeInsets.only(top: 4),
                                    child: Text(
                                      entry.notes,
                                      style: TextStyle(
                                        fontStyle: FontStyle.italic,
                                        color: Colors.grey[700],
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                            trailing: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                '${entry.duration} min',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: Theme.of(context).colorScheme.tertiary,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          FloatingActionButton(
            heroTag: "quick_exercise",
            onPressed: _quickLogExercise,
            backgroundColor: Theme.of(context).colorScheme.secondary,
            foregroundColor: Colors.white,
            child: const Icon(Icons.fitness_center),
          ),
          const SizedBox(height: 16),
          FloatingActionButton.extended(
            heroTag: "log_workout",
            onPressed: _addWorkoutEntry,
            icon: const Icon(Icons.add),
            label: const Text('Log Workout'),
            backgroundColor: Theme.of(context).colorScheme.tertiary,
            foregroundColor: Colors.white,
          ),
        ],
      ),
    );
  }

  Future<void> _quickLogExercise() async {
    try {
      final exercises = await _databaseService.getAllExercises();

      if (mounted && exercises.isNotEmpty) {
        final selectedExercise = await showDialog<Exercise>(
          context: context,
          builder: (context) => AlertDialog(
            title: const Text('Quick Exercise Log'),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: ListView.builder(
                itemCount: exercises.length,
                itemBuilder: (context, index) {
                  final exercise = exercises[index];
                  return ListTile(
                    leading: Icon(
                      Icons.fitness_center,
                      color: Theme.of(context).colorScheme.primary,
                    ),
                    title: Text(exercise.name),
                    subtitle: Text(exercise.muscleGroupsDisplay),
                    onTap: () => Navigator.of(context).pop(exercise),
                  );
                },
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('Cancel'),
              ),
            ],
          ),
        );

        if (selectedExercise != null && mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (context) => ExerciseLoggingScreen(
                exercise: selectedExercise,
              ),
            ),
          ).then((_) => _loadWorkouts()); // Refresh after logging
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading exercises: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildAnalyticsItem(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 10,
              color: color.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  String _getAverageDuration() {
    final completedSessions = _workoutSessions
        .where((s) => s.status == SessionStatus.completed && s.duration != null)
        .toList();

    if (completedSessions.isEmpty) return '0m';

    final totalMinutes = completedSessions
        .fold(0, (sum, s) => sum + s.duration!.inMinutes);

    final avgMinutes = totalMinutes ~/ completedSessions.length;
    return '${avgMinutes}m';
  }

  Widget _buildStatInfo(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color.withValues(alpha: 0.8),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}

class _AddWorkoutDialog extends StatefulWidget {
  final Function(String, int, String) onAdd;

  const _AddWorkoutDialog({required this.onAdd});

  @override
  State<_AddWorkoutDialog> createState() => _AddWorkoutDialogState();
}

class _AddWorkoutDialogState extends State<_AddWorkoutDialog> {
  final _exerciseController = TextEditingController();
  final _durationController = TextEditingController();
  final _notesController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      title: Row(
        children: [
          Icon(
            Icons.fitness_center,
            color: Theme.of(context).colorScheme.tertiary,
          ),
          const SizedBox(width: 8),
          const Text('Log Workout'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _exerciseController,
            decoration: InputDecoration(
              labelText: 'Exercise/Activity',
              hintText: 'e.g., Running, Yoga, Weight Training',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.sports_gymnastics),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _durationController,
            decoration: InputDecoration(
              labelText: 'Duration',
              hintText: 'e.g., 30',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.timer),
              suffixText: 'minutes',
            ),
            keyboardType: TextInputType.number,
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _notesController,
            decoration: InputDecoration(
              labelText: 'Notes (optional)',
              hintText: 'How did it go?',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.note_alt),
            ),
            maxLines: 2,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final exercise = _exerciseController.text.trim();
            final duration = int.tryParse(_durationController.text) ?? 0;
            final notes = _notesController.text.trim();

            if (exercise.isNotEmpty && duration > 0) {
              widget.onAdd(exercise, duration, notes);
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.tertiary,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          child: const Text('Log'),
        ),
      ],
    );
  }
}
