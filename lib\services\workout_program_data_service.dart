import '../models/workout_program.dart';
import '../models/exercise.dart';
import '../services/database_service.dart';

class WorkoutProgramDataService {
  static final WorkoutProgramDataService _instance = WorkoutProgramDataService._internal();
  factory WorkoutProgramDataService() => _instance;
  WorkoutProgramDataService._internal();

  final DatabaseService _databaseService = DatabaseService();

  /// Initialize predefined workout programs
  Future<void> initializeWorkoutPrograms() async {
    // Check if programs are already loaded (beyond the default ones)
    final existingPrograms = await _databaseService.getAllWorkoutPrograms();
    if (existingPrograms.length > 2) {
      return; // Already initialized with comprehensive programs
    }

    // Load all program categories
    await _loadBeginnerPrograms();
    await _loadIntermediatePrograms();
    await _loadAdvancedPrograms();
    await _loadSpecialtyPrograms();
  }

  Future<void> _loadBeginnerPrograms() async {
    final programs = [
      WorkoutProgram(
        name: "Nonni's First Steps",
        description: "Perfect introduction to fitness with gentle bodyweight exercises. Build confidence and establish healthy habits with this beginner-friendly program.",
        difficulty: DifficultyLevel.beginner,
        type: ProgramType.fullBody,
        durationWeeks: 4,
        workoutsPerWeek: 3,
        requiredEquipment: [],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
      WorkoutProgram(
        name: "Gentle Yoga Journey",
        description: "Discover flexibility, balance, and inner peace with this beginner yoga program. Perfect for stress relief and building body awareness.",
        difficulty: DifficultyLevel.beginner,
        type: ProgramType.yoga,
        durationWeeks: 6,
        workoutsPerWeek: 3,
        requiredEquipment: [],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
      WorkoutProgram(
        name: "Cardio Kickstart",
        description: "Low-impact cardio program to improve heart health and endurance. Start your fitness journey with fun, energizing workouts.",
        difficulty: DifficultyLevel.beginner,
        type: ProgramType.cardio,
        durationWeeks: 4,
        workoutsPerWeek: 4,
        requiredEquipment: [],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
    ];

    for (final program in programs) {
      await _insertWorkoutProgram(program);
    }
  }

  Future<void> _loadIntermediatePrograms() async {
    final programs = [
      WorkoutProgram(
        name: "Nonni's Strength Builder",
        description: "Build lean muscle and increase strength with this progressive program. Combines bodyweight and dumbbell exercises for balanced development.",
        difficulty: DifficultyLevel.intermediate,
        type: ProgramType.strength,
        durationWeeks: 8,
        workoutsPerWeek: 4,
        requiredEquipment: [Equipment.dumbbells],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
      WorkoutProgram(
        name: "Upper/Lower Split",
        description: "Efficient training split focusing on upper body and lower body on alternating days. Perfect for building strength and muscle definition.",
        difficulty: DifficultyLevel.intermediate,
        type: ProgramType.upperLower,
        durationWeeks: 6,
        workoutsPerWeek: 4,
        requiredEquipment: [Equipment.dumbbells, Equipment.barbell],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
      WorkoutProgram(
        name: "HIIT Power Sessions",
        description: "High-intensity interval training to burn calories and improve cardiovascular fitness. Short, intense workouts that fit any schedule.",
        difficulty: DifficultyLevel.intermediate,
        type: ProgramType.hiit,
        durationWeeks: 6,
        workoutsPerWeek: 3,
        requiredEquipment: [],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
      WorkoutProgram(
        name: "Functional Fitness",
        description: "Real-world movement patterns to improve daily life activities. Focus on mobility, stability, and functional strength.",
        difficulty: DifficultyLevel.intermediate,
        type: ProgramType.fullBody,
        durationWeeks: 8,
        workoutsPerWeek: 3,
        requiredEquipment: [Equipment.kettlebell, Equipment.resistanceBand],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
    ];

    for (final program in programs) {
      await _insertWorkoutProgram(program);
    }
  }

  Future<void> _loadAdvancedPrograms() async {
    final programs = [
      WorkoutProgram(
        name: "Push/Pull/Legs Elite",
        description: "Advanced training split for serious strength and muscle building. Maximize your potential with this comprehensive program.",
        difficulty: DifficultyLevel.advanced,
        type: ProgramType.pushPullLegs,
        durationWeeks: 12,
        workoutsPerWeek: 6,
        requiredEquipment: [Equipment.barbell, Equipment.dumbbells, Equipment.pullUpBar],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
      WorkoutProgram(
        name: "Athletic Performance",
        description: "Sport-specific training for peak athletic performance. Combines strength, power, agility, and conditioning.",
        difficulty: DifficultyLevel.advanced,
        type: ProgramType.strength,
        durationWeeks: 10,
        workoutsPerWeek: 5,
        requiredEquipment: [Equipment.barbell, Equipment.dumbbells, Equipment.kettlebell],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
      WorkoutProgram(
        name: "Advanced HIIT Challenge",
        description: "Extreme high-intensity training for experienced athletes. Push your limits with complex movement patterns and intense intervals.",
        difficulty: DifficultyLevel.advanced,
        type: ProgramType.hiit,
        durationWeeks: 8,
        workoutsPerWeek: 4,
        requiredEquipment: [Equipment.kettlebell, Equipment.resistanceBand],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
    ];

    for (final program in programs) {
      await _insertWorkoutProgram(program);
    }
  }

  Future<void> _loadSpecialtyPrograms() async {
    final programs = [
      WorkoutProgram(
        name: "Nonni's Wellness Flow",
        description: "Holistic approach combining yoga, meditation, and gentle movement. Perfect for stress relief and overall well-being.",
        difficulty: DifficultyLevel.beginner,
        type: ProgramType.yoga,
        durationWeeks: 8,
        workoutsPerWeek: 4,
        requiredEquipment: [],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
      WorkoutProgram(
        name: "Core & Posture Fix",
        description: "Strengthen your core and improve posture with targeted exercises. Great for desk workers and anyone with back issues.",
        difficulty: DifficultyLevel.intermediate,
        type: ProgramType.fullBody,
        durationWeeks: 6,
        workoutsPerWeek: 3,
        requiredEquipment: [Equipment.resistanceBand],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
      WorkoutProgram(
        name: "Fat Burn Express",
        description: "Quick, effective workouts designed to maximize calorie burn. Perfect for busy schedules and weight loss goals.",
        difficulty: DifficultyLevel.intermediate,
        type: ProgramType.hiit,
        durationWeeks: 4,
        workoutsPerWeek: 5,
        requiredEquipment: [],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
      WorkoutProgram(
        name: "Flexibility & Mobility",
        description: "Improve range of motion and reduce stiffness with this comprehensive flexibility program. Essential for injury prevention.",
        difficulty: DifficultyLevel.beginner,
        type: ProgramType.flexibility,
        durationWeeks: 6,
        workoutsPerWeek: 4,
        requiredEquipment: [Equipment.resistanceBand],
        imageUrl: '',
        isCustom: false,
        createdAt: DateTime.now(),
      ),
    ];

    for (final program in programs) {
      await _insertWorkoutProgram(program);
    }
  }

  Future<int> _insertWorkoutProgram(WorkoutProgram program) async {
    final db = await _databaseService.database;
    return await db.insert('workout_programs', program.toMap());
  }

  /// Get programs filtered by difficulty level
  Future<List<WorkoutProgram>> getProgramsByDifficulty(DifficultyLevel difficulty) async {
    final allPrograms = await _databaseService.getAllWorkoutPrograms();
    return allPrograms.where((program) => program.difficulty == difficulty).toList();
  }

  /// Get programs filtered by type
  Future<List<WorkoutProgram>> getProgramsByType(ProgramType type) async {
    final allPrograms = await _databaseService.getAllWorkoutPrograms();
    return allPrograms.where((program) => program.type == type).toList();
  }

  /// Get programs that require specific equipment
  Future<List<WorkoutProgram>> getProgramsByEquipment(List<Equipment> availableEquipment) async {
    final allPrograms = await _databaseService.getAllWorkoutPrograms();
    return allPrograms.where((program) {
      return program.requiredEquipment.every((required) => 
        availableEquipment.contains(required) || required == Equipment.none);
    }).toList();
  }

  /// Get recommended programs based on user preferences
  Future<List<WorkoutProgram>> getRecommendedPrograms({
    DifficultyLevel? difficulty,
    ProgramType? type,
    List<Equipment>? availableEquipment,
    int? maxWeeks,
    int? maxWorkoutsPerWeek,
  }) async {
    final allPrograms = await _databaseService.getAllWorkoutPrograms();
    
    return allPrograms.where((program) {
      if (difficulty != null && program.difficulty != difficulty) return false;
      if (type != null && program.type != type) return false;
      if (maxWeeks != null && program.durationWeeks > maxWeeks) return false;
      if (maxWorkoutsPerWeek != null && program.workoutsPerWeek > maxWorkoutsPerWeek) return false;
      
      if (availableEquipment != null) {
        final hasRequiredEquipment = program.requiredEquipment.every((required) => 
          availableEquipment.contains(required) || required == Equipment.none);
        if (!hasRequiredEquipment) return false;
      }
      
      return true;
    }).toList();
  }
}
