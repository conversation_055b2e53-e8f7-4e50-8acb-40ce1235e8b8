# <PERSON><PERSON>'s Health Journey 💪❤️

A beautiful, personalized health and fitness companion built with Flutter, designed specifically for <PERSON><PERSON> to track nutrition and training progress with love.

## Features

- **<PERSON><PERSON>'s Nutrition Tracker**: Log food intake and monitor daily calorie consumption with beautiful, intuitive interface
  - **🔍 Barcode Scanner**: Scan product barcodes to automatically fetch nutritional information
  - **🇳🇱 Netherlands Support**: Optimized for Dutch products using OpenFoodFacts database
  - **📊 Smart Calculations**: Automatic calorie calculation based on product weight/amount
- **<PERSON><PERSON>'s Fitness Journey**: Advanced workout tracking with detailed exercise logging
  - **💪 Exercise Database**: Pre-loaded exercises with muscle groups and equipment info
  - **📊 Set/Rep/Weight Tracking**: Log detailed workout data with sets, reps, and weights
  - **⏱️ Rest Timer Integration**: Built-in rest time tracking between sets
  - **📈 Progress Analytics**: Track volume, strength gains, and workout consistency
  - **🎯 Structured Programs**: Follow curated workout programs with different difficulty levels
- **<PERSON><PERSON>'s Training Programs**: Access carefully curated workout programs for different fitness levels
- **Persistent Data Storage**: All progress is saved locally using SQLite database
- **Beautiful UI**: Gradient cards, smooth animations, and personalized messaging throughout

## Setup Instructions

### Prerequisites

1. **Install Flutter**: Follow the official guide at https://docs.flutter.dev/get-started/install/windows
2. **Install Android Studio**: Required for Android development
3. **Set up Android SDK**: Configure through Android Studio

### Running the App

1. **Clone/Navigate to the project directory**
   ```bash
   cd mobilenon
   ```

2. **Get Flutter dependencies**
   ```bash
   flutter pub get
   ```

3. **Check Flutter setup**
   ```bash
   flutter doctor
   ```

4. **Connect an Android device or start an emulator**

5. **Run the app**
   ```bash
   flutter run
   ```

### Building for Release

To build an APK for distribution:
```bash
flutter build apk --release
```

The APK will be generated in `build/app/outputs/flutter-apk/app-release.apk`

## App Structure

- `lib/main.dart` - App entry point
- `lib/screens/` - All screen widgets
  - `home_screen.dart` - Main navigation with bottom tabs
  - `calorie_tracking_screen.dart` - Food logging and calorie tracking
  - `training_progress_screen.dart` - Workout logging and progress stats
  - `training_programs_screen.dart` - Predefined workout programs

## Dependencies

- `sqflite` - Local database for data persistence
- `shared_preferences` - Simple key-value storage
- `intl` - Date formatting and internationalization
- `fl_chart` - Charts and graphs for progress visualization
- `mobile_scanner` - Barcode scanning functionality
- `http` - API calls to OpenFoodFacts database
- `permission_handler` - Camera permissions for barcode scanning

## Next Steps

1. Install Flutter following the official guide
2. Run `flutter pub get` to install dependencies
3. Test the app on an Android device or emulator
4. Customize the training programs and add more features as needed

## How to Use Barcode Scanner

1. **Open Nutrition Tracker**: Go to "Nonni's Nutrition" tab
2. **Add Food**: Tap the "Add Food" button
3. **Scan Barcode**: Tap "Scan Barcode" button in the dialog
4. **Position Product**: Point camera at the barcode until it scans
5. **Adjust Amount**: Enter the actual weight/amount you consumed
6. **Add to Log**: Calories are automatically calculated and added

**Supported Products**: Works best with products sold in The Netherlands, but supports international products through OpenFoodFacts database.

## How to Use Advanced Workout Tracking

### **Quick Exercise Logging:**
1. **Open Fitness Tab**: Go to "Nonni's Fitness" tab
2. **Quick Log**: Tap the small fitness center icon (floating button)
3. **Select Exercise**: Choose from pre-loaded exercise database
4. **Log Sets**: Add sets with reps, weights, and rest times
5. **Track Progress**: View volume and strength progression over time

### **Structured Workout Programs:**
1. **Browse Programs**: Go to "Nonni's Programs" tab
2. **Select Program**: Choose based on fitness level and goals
3. **Follow Schedule**: Get guided workout sessions
4. **Track Completion**: Monitor program progress and consistency

### **Exercise Database Features:**
- **Muscle Groups**: Exercises categorized by target muscles
- **Equipment Types**: Bodyweight, dumbbells, machines, etc.
- **Exercise Instructions**: Step-by-step guidance for proper form
- **Default Settings**: Smart defaults for sets, reps, and rest times

## Customization Ideas

- Add more detailed nutrition tracking (proteins, carbs, fats)
- Implement data export/import functionality
- Add progress photos feature
- Include weight tracking with charts
- Add meal planning and recipes
- Implement reminders and notifications
- Add custom product database for frequently used items
