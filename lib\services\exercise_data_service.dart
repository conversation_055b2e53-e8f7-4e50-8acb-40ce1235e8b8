import '../models/exercise.dart';
import '../services/database_service.dart';

class ExerciseDataService {
  static final ExerciseDataService _instance = ExerciseDataService._internal();
  factory ExerciseDataService() => _instance;
  ExerciseDataService._internal();

  final DatabaseService _databaseService = DatabaseService();

  /// Initialize the exercise database with comprehensive exercise library
  Future<void> initializeExerciseLibrary() async {
    // Check if exercises are already loaded
    final existingExercises = await _databaseService.getAllExercises();
    if (existingExercises.isNotEmpty) {
      return; // Already initialized
    }

    // Load all exercise categories
    await _loadStrengthExercises();
    await _loadLegExercises();
    await _loadCardioExercises();
    await _loadHIITExercises();
    await _loadYogaExercises();
  }

  Future<void> _loadStrengthExercises() async {
    final exercises = [
      // CHEST EXERCISES
      Exercise(
        name: 'Push-ups',
        description: 'Classic bodyweight chest exercise',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.chest, MuscleGroup.triceps, MuscleGroup.shoulders],
        equipment: Equipment.none,
        instructions: '1. Start in plank position\n2. Lower chest to ground\n3. Push back up\n4. Keep core tight throughout',
        defaultSets: 3,
        defaultReps: 12,
        defaultWeight: 0.0,
        restTime: 60,
      ),
      Exercise(
        name: 'Bench Press',
        description: 'Fundamental chest building exercise',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.chest, MuscleGroup.triceps, MuscleGroup.shoulders],
        equipment: Equipment.barbell,
        instructions: '1. Lie on bench, grip bar wider than shoulders\n2. Lower bar to chest\n3. Press up explosively\n4. Keep feet planted',
        defaultSets: 4,
        defaultReps: 8,
        defaultWeight: 60.0,
        restTime: 120,
      ),
      Exercise(
        name: 'Dumbbell Flyes',
        description: 'Isolation exercise for chest development',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.chest],
        equipment: Equipment.dumbbells,
        instructions: '1. Lie on bench with dumbbells\n2. Arc arms wide, feeling chest stretch\n3. Bring dumbbells together above chest\n4. Control the movement',
        defaultSets: 3,
        defaultReps: 12,
        defaultWeight: 15.0,
        restTime: 90,
      ),
      Exercise(
        name: 'Incline Dumbbell Press',
        description: 'Upper chest focused pressing movement',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.chest, MuscleGroup.shoulders, MuscleGroup.triceps],
        equipment: Equipment.dumbbells,
        instructions: '1. Set bench to 30-45 degree incline\n2. Press dumbbells from chest level\n3. Focus on upper chest contraction\n4. Control the descent',
        defaultSets: 4,
        defaultReps: 10,
        defaultWeight: 20.0,
        restTime: 90,
      ),

      // BACK EXERCISES
      Exercise(
        name: 'Pull-ups',
        description: 'Ultimate upper body pulling exercise',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.back, MuscleGroup.biceps],
        equipment: Equipment.pullUpBar,
        instructions: '1. Hang from bar with overhand grip\n2. Pull body up until chin over bar\n3. Lower with control\n4. Full range of motion',
        defaultSets: 3,
        defaultReps: 8,
        defaultWeight: 0.0,
        restTime: 120,
      ),
      Exercise(
        name: 'Deadlifts',
        description: 'King of all exercises - full body strength',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.back, MuscleGroup.legs, MuscleGroup.glutes, MuscleGroup.core],
        equipment: Equipment.barbell,
        instructions: '1. Stand with bar over mid-foot\n2. Grip bar, keep back straight\n3. Drive through heels to stand\n4. Reverse movement to lower',
        defaultSets: 4,
        defaultReps: 6,
        defaultWeight: 80.0,
        restTime: 180,
      ),
      Exercise(
        name: 'Bent-over Rows',
        description: 'Horizontal pulling for back thickness',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.back, MuscleGroup.biceps],
        equipment: Equipment.barbell,
        instructions: '1. Hinge at hips, hold bar\n2. Pull bar to lower chest\n3. Squeeze shoulder blades\n4. Lower with control',
        defaultSets: 4,
        defaultReps: 10,
        defaultWeight: 50.0,
        restTime: 90,
      ),
      Exercise(
        name: 'Lat Pulldowns',
        description: 'Vertical pulling for lat development',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.back, MuscleGroup.biceps],
        equipment: Equipment.cableMachine,
        instructions: '1. Sit at lat pulldown machine\n2. Grip bar wider than shoulders\n3. Pull to upper chest\n4. Feel lats working',
        defaultSets: 3,
        defaultReps: 12,
        defaultWeight: 40.0,
        restTime: 90,
      ),

      // SHOULDER EXERCISES
      Exercise(
        name: 'Overhead Press',
        description: 'Primary shoulder building movement',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.shoulders, MuscleGroup.triceps, MuscleGroup.core],
        equipment: Equipment.barbell,
        instructions: '1. Stand with bar at shoulder level\n2. Press straight overhead\n3. Keep core tight\n4. Lower with control',
        defaultSets: 4,
        defaultReps: 8,
        defaultWeight: 40.0,
        restTime: 120,
      ),
      Exercise(
        name: 'Lateral Raises',
        description: 'Isolation for shoulder width',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.shoulders],
        equipment: Equipment.dumbbells,
        instructions: '1. Hold dumbbells at sides\n2. Raise arms to shoulder height\n3. Lead with pinkies\n4. Control the descent',
        defaultSets: 3,
        defaultReps: 15,
        defaultWeight: 8.0,
        restTime: 60,
      ),
      Exercise(
        name: 'Face Pulls',
        description: 'Rear delt and posture exercise',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.shoulders, MuscleGroup.back],
        equipment: Equipment.cableMachine,
        instructions: '1. Set cable at face height\n2. Pull rope to face\n3. Separate hands at face\n4. Squeeze rear delts',
        defaultSets: 3,
        defaultReps: 15,
        defaultWeight: 15.0,
        restTime: 60,
      ),

      // ARM EXERCISES
      Exercise(
        name: 'Bicep Curls',
        description: 'Classic bicep building exercise',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.biceps],
        equipment: Equipment.dumbbells,
        instructions: '1. Hold dumbbells at sides\n2. Curl up with control\n3. Squeeze at top\n4. Lower slowly',
        defaultSets: 3,
        defaultReps: 12,
        defaultWeight: 12.0,
        restTime: 60,
      ),
      Exercise(
        name: 'Tricep Dips',
        description: 'Bodyweight tricep exercise',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.triceps, MuscleGroup.shoulders],
        equipment: Equipment.none,
        instructions: '1. Hands on bench behind you\n2. Lower body by bending elbows\n3. Push back up\n4. Keep body close to bench',
        defaultSets: 3,
        defaultReps: 12,
        defaultWeight: 0.0,
        restTime: 60,
      ),
      Exercise(
        name: 'Close-grip Push-ups',
        description: 'Tricep-focused push-up variation',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.triceps, MuscleGroup.chest],
        equipment: Equipment.none,
        instructions: '1. Push-up position, hands close together\n2. Lower chest to hands\n3. Push up focusing on triceps\n4. Keep elbows close to body',
        defaultSets: 3,
        defaultReps: 10,
        defaultWeight: 0.0,
        restTime: 60,
      ),
    ];

    for (final exercise in exercises) {
      await _databaseService.insertExercise(exercise);
    }
  }

  Future<void> _loadCardioExercises() async {
    final exercises = [
      Exercise(
        name: 'Running',
        description: 'Classic cardiovascular exercise',
        type: ExerciseType.cardio,
        muscleGroups: [MuscleGroup.legs, MuscleGroup.fullBody],
        equipment: Equipment.none,
        instructions: '1. Start with light jog\n2. Maintain steady pace\n3. Focus on breathing\n4. Land on midfoot',
        defaultSets: 1,
        defaultReps: 1,
        defaultWeight: 0.0,
        defaultDuration: 1800, // 30 minutes
        restTime: 0,
      ),
      Exercise(
        name: 'Cycling',
        description: 'Low-impact cardio exercise',
        type: ExerciseType.cardio,
        muscleGroups: [MuscleGroup.legs, MuscleGroup.glutes],
        equipment: Equipment.other,
        instructions: '1. Adjust bike to proper height\n2. Maintain steady cadence\n3. Use proper posture\n4. Vary resistance as needed',
        defaultSets: 1,
        defaultReps: 1,
        defaultWeight: 0.0,
        defaultDuration: 2400, // 40 minutes
        restTime: 0,
      ),
      Exercise(
        name: 'Jump Rope',
        description: 'High-intensity cardio exercise',
        type: ExerciseType.cardio,
        muscleGroups: [MuscleGroup.fullBody, MuscleGroup.legs],
        equipment: Equipment.other,
        instructions: '1. Hold rope handles lightly\n2. Jump on balls of feet\n3. Keep elbows close to body\n4. Maintain rhythm',
        defaultSets: 3,
        defaultReps: 1,
        defaultWeight: 0.0,
        defaultDuration: 300, // 5 minutes per set
        restTime: 60,
      ),
      Exercise(
        name: 'Rowing',
        description: 'Full-body cardio exercise',
        type: ExerciseType.cardio,
        muscleGroups: [MuscleGroup.fullBody, MuscleGroup.back, MuscleGroup.legs],
        equipment: Equipment.other,
        instructions: '1. Sit tall on rowing machine\n2. Drive with legs first\n3. Pull handle to chest\n4. Reverse the movement',
        defaultSets: 1,
        defaultReps: 1,
        defaultWeight: 0.0,
        defaultDuration: 1200, // 20 minutes
        restTime: 0,
      ),
    ];

    for (final exercise in exercises) {
      await _databaseService.insertExercise(exercise);
    }
  }

  Future<void> _loadHIITExercises() async {
    final exercises = [
      Exercise(
        name: 'Burpees',
        description: 'Full-body explosive movement',
        type: ExerciseType.hiit,
        muscleGroups: [MuscleGroup.fullBody],
        equipment: Equipment.none,
        instructions: '1. Start standing\n2. Drop to squat, hands on floor\n3. Jump feet back to plank\n4. Jump feet forward, then jump up',
        defaultSets: 4,
        defaultReps: 10,
        defaultWeight: 0.0,
        defaultDuration: 30,
        restTime: 30,
      ),
      Exercise(
        name: 'Mountain Climbers',
        description: 'High-intensity core and cardio',
        type: ExerciseType.hiit,
        muscleGroups: [MuscleGroup.core, MuscleGroup.fullBody],
        equipment: Equipment.none,
        instructions: '1. Start in plank position\n2. Alternate bringing knees to chest\n3. Keep hips level\n4. Move as fast as possible',
        defaultSets: 4,
        defaultReps: 20,
        defaultWeight: 0.0,
        defaultDuration: 30,
        restTime: 30,
      ),
      Exercise(
        name: 'High Knees',
        description: 'Cardio and leg strengthening',
        type: ExerciseType.hiit,
        muscleGroups: [MuscleGroup.legs, MuscleGroup.core],
        equipment: Equipment.none,
        instructions: '1. Stand tall\n2. Run in place lifting knees high\n3. Pump arms actively\n4. Land on balls of feet',
        defaultSets: 4,
        defaultReps: 30,
        defaultWeight: 0.0,
        defaultDuration: 30,
        restTime: 30,
      ),
      Exercise(
        name: 'Jump Squats',
        description: 'Explosive lower body power',
        type: ExerciseType.hiit,
        muscleGroups: [MuscleGroup.legs, MuscleGroup.glutes],
        equipment: Equipment.none,
        instructions: '1. Start in squat position\n2. Jump up explosively\n3. Land softly back in squat\n4. Repeat immediately',
        defaultSets: 4,
        defaultReps: 12,
        defaultWeight: 0.0,
        defaultDuration: 30,
        restTime: 30,
      ),
    ];

    for (final exercise in exercises) {
      await _databaseService.insertExercise(exercise);
    }
  }

  Future<void> _loadYogaExercises() async {
    final exercises = [
      Exercise(
        name: 'Downward Dog',
        description: 'Classic yoga pose for flexibility',
        type: ExerciseType.flexibility,
        muscleGroups: [MuscleGroup.fullBody, MuscleGroup.shoulders],
        equipment: Equipment.none,
        instructions: '1. Start on hands and knees\n2. Tuck toes, lift hips up\n3. Straighten legs and arms\n4. Hold and breathe deeply',
        defaultSets: 3,
        defaultReps: 1,
        defaultWeight: 0.0,
        defaultDuration: 60,
        restTime: 30,
      ),
      Exercise(
        name: 'Warrior I',
        description: 'Standing yoga pose for strength',
        type: ExerciseType.flexibility,
        muscleGroups: [MuscleGroup.legs, MuscleGroup.core],
        equipment: Equipment.none,
        instructions: '1. Step one foot forward into lunge\n2. Turn back foot out 45 degrees\n3. Raise arms overhead\n4. Hold and switch sides',
        defaultSets: 2,
        defaultReps: 1,
        defaultWeight: 0.0,
        defaultDuration: 45,
        restTime: 15,
      ),
      Exercise(
        name: 'Plank Pose',
        description: 'Core strengthening yoga pose',
        type: ExerciseType.flexibility,
        muscleGroups: [MuscleGroup.core, MuscleGroup.fullBody],
        equipment: Equipment.none,
        instructions: '1. Start in push-up position\n2. Keep body in straight line\n3. Engage core muscles\n4. Breathe steadily',
        defaultSets: 3,
        defaultReps: 1,
        defaultWeight: 0.0,
        defaultDuration: 60,
        restTime: 30,
      ),
      Exercise(
        name: 'Child\'s Pose',
        description: 'Restorative yoga pose',
        type: ExerciseType.flexibility,
        muscleGroups: [MuscleGroup.back, MuscleGroup.shoulders],
        equipment: Equipment.none,
        instructions: '1. Kneel on floor\n2. Sit back on heels\n3. Fold forward, arms extended\n4. Rest forehead on ground',
        defaultSets: 1,
        defaultReps: 1,
        defaultWeight: 0.0,
        defaultDuration: 120,
        restTime: 0,
      ),
    ];

    for (final exercise in exercises) {
      await _databaseService.insertExercise(exercise);
    }
  }

  Future<void> _loadLegExercises() async {
    final exercises = [
      Exercise(
        name: 'Squats',
        description: 'King of leg exercises',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.legs, MuscleGroup.glutes],
        equipment: Equipment.none,
        instructions: '1. Stand with feet shoulder-width apart\n2. Lower hips back and down\n3. Keep chest up and knees tracking over toes\n4. Drive through heels to stand',
        defaultSets: 4,
        defaultReps: 12,
        defaultWeight: 0.0,
        restTime: 90,
      ),
      Exercise(
        name: 'Lunges',
        description: 'Unilateral leg strengthening',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.legs, MuscleGroup.glutes],
        equipment: Equipment.none,
        instructions: '1. Step forward into lunge position\n2. Lower back knee toward ground\n3. Keep front knee over ankle\n4. Push back to starting position',
        defaultSets: 3,
        defaultReps: 10,
        defaultWeight: 0.0,
        restTime: 60,
      ),
      Exercise(
        name: 'Calf Raises',
        description: 'Calf muscle strengthening',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.legs],
        equipment: Equipment.none,
        instructions: '1. Stand tall on balls of feet\n2. Rise up onto toes\n3. Hold briefly at top\n4. Lower with control',
        defaultSets: 3,
        defaultReps: 20,
        defaultWeight: 0.0,
        restTime: 45,
      ),
      Exercise(
        name: 'Bulgarian Split Squats',
        description: 'Advanced single-leg exercise',
        type: ExerciseType.strength,
        muscleGroups: [MuscleGroup.legs, MuscleGroup.glutes],
        equipment: Equipment.none,
        instructions: '1. Place rear foot on bench\n2. Lower into single-leg squat\n3. Keep torso upright\n4. Drive through front heel',
        defaultSets: 3,
        defaultReps: 8,
        defaultWeight: 0.0,
        restTime: 90,
      ),
    ];

    for (final exercise in exercises) {
      await _databaseService.insertExercise(exercise);
    }
  }
}
