class ExerciseSet {
  final int? id;
  final int exerciseLogId;
  final int setNumber;
  final int reps;
  final double weight; // in kg
  final int duration; // in seconds for cardio/timed exercises
  final double distance; // in meters for cardio
  final int restTime; // in seconds
  final bool completed;
  final String notes;
  final DateTime timestamp;

  ExerciseSet({
    this.id,
    required this.exerciseLogId,
    required this.setNumber,
    required this.reps,
    required this.weight,
    this.duration = 0,
    this.distance = 0.0,
    this.restTime = 60,
    this.completed = false,
    this.notes = '',
    required this.timestamp,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'exercise_log_id': exerciseLogId,
      'set_number': setNumber,
      'reps': reps,
      'weight': weight,
      'duration': duration,
      'distance': distance,
      'rest_time': restTime,
      'completed': completed ? 1 : 0,
      'notes': notes,
      'timestamp': timestamp.millisecondsSinceEpoch,
    };
  }

  factory ExerciseSet.fromMap(Map<String, dynamic> map) {
    return ExerciseSet(
      id: map['id'],
      exerciseLogId: map['exercise_log_id'],
      setNumber: map['set_number'],
      reps: map['reps'],
      weight: map['weight']?.toDouble() ?? 0.0,
      duration: map['duration'] ?? 0,
      distance: map['distance']?.toDouble() ?? 0.0,
      restTime: map['rest_time'] ?? 60,
      completed: map['completed'] == 1,
      notes: map['notes'] ?? '',
      timestamp: DateTime.fromMillisecondsSinceEpoch(map['timestamp']),
    );
  }

  ExerciseSet copyWith({
    int? id,
    int? exerciseLogId,
    int? setNumber,
    int? reps,
    double? weight,
    int? duration,
    double? distance,
    int? restTime,
    bool? completed,
    String? notes,
    DateTime? timestamp,
  }) {
    return ExerciseSet(
      id: id ?? this.id,
      exerciseLogId: exerciseLogId ?? this.exerciseLogId,
      setNumber: setNumber ?? this.setNumber,
      reps: reps ?? this.reps,
      weight: weight ?? this.weight,
      duration: duration ?? this.duration,
      distance: distance ?? this.distance,
      restTime: restTime ?? this.restTime,
      completed: completed ?? this.completed,
      notes: notes ?? this.notes,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  String get displayText {
    if (duration > 0) {
      // Cardio/timed exercise
      final minutes = duration ~/ 60;
      final seconds = duration % 60;
      return '${minutes}m ${seconds}s';
    } else {
      // Strength exercise
      if (weight > 0) {
        return '$reps reps × ${weight.toStringAsFixed(1)}kg';
      } else {
        return '$reps reps';
      }
    }
  }

  double get volume {
    return reps * weight;
  }

  String get formattedRestTime {
    final minutes = restTime ~/ 60;
    final seconds = restTime % 60;
    if (minutes > 0) {
      return '${minutes}m ${seconds}s';
    } else {
      return '${seconds}s';
    }
  }
}
