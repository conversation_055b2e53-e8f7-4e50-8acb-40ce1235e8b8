class ProgramWorkout {
  final int? id;
  final int programId;
  final String name;
  final String description;
  final int dayOfWeek; // 1 = Monday, 7 = Sunday
  final int weekNumber;
  final int orderIndex;

  ProgramWorkout({
    this.id,
    required this.programId,
    required this.name,
    required this.description,
    required this.dayOfWeek,
    this.weekNumber = 1,
    this.orderIndex = 0,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'program_id': programId,
      'name': name,
      'description': description,
      'day_of_week': dayOfWeek,
      'week_number': weekNumber,
      'order_index': orderIndex,
    };
  }

  factory ProgramWorkout.fromMap(Map<String, dynamic> map) {
    return ProgramWorkout(
      id: map['id'],
      programId: map['program_id'],
      name: map['name'],
      description: map['description'] ?? '',
      dayOfWeek: map['day_of_week'],
      weekNumber: map['week_number'] ?? 1,
      orderIndex: map['order_index'] ?? 0,
    );
  }

  String get dayName {
    switch (dayOfWeek) {
      case 1:
        return 'Monday';
      case 2:
        return 'Tuesday';
      case 3:
        return 'Wednesday';
      case 4:
        return 'Thursday';
      case 5:
        return 'Friday';
      case 6:
        return 'Saturday';
      case 7:
        return 'Sunday';
      default:
        return 'Unknown';
    }
  }

  ProgramWorkout copyWith({
    int? id,
    int? programId,
    String? name,
    String? description,
    int? dayOfWeek,
    int? weekNumber,
    int? orderIndex,
  }) {
    return ProgramWorkout(
      id: id ?? this.id,
      programId: programId ?? this.programId,
      name: name ?? this.name,
      description: description ?? this.description,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
      weekNumber: weekNumber ?? this.weekNumber,
      orderIndex: orderIndex ?? this.orderIndex,
    );
  }
}
