import '../models/workout_program.dart';
import '../models/user_active_program.dart';
import '../models/nutrition_recommendation.dart';

class NutritionService {
  // Base metabolic rate calculation (simplified <PERSON><PERSON>Benedict equation)
  // Using average values for a moderately active woman
  static const int _baseBMR = 1400;
  static const double _activityMultiplier = 1.4; // Moderately active

  /// Calculate nutrition recommendations based on workout program
  static NutritionRecommendation calculateRecommendation({
    required WorkoutProgram program,
    NutritionGoal? userGoal,
    int? currentWeight, // in kg
    int? targetWeight, // in kg
  }) {
    // Determine nutrition goal from program type if not specified
    final goal = userGoal ?? _determineGoalFromProgram(program);
    
    // Calculate base calories
    int baseCalories = (_baseBMR * _activityMultiplier).round();
    
    // Adjust calories based on program intensity and goal
    final calorieAdjustment = _calculateCalorieAdjustment(program, goal);
    final dailyCalories = (baseCalories + calorieAdjustment).round();
    
    // Calculate macronutrients based on goal and program type
    final macros = _calculateMacronutrients(dailyCalories, goal, program);
    
    // Generate reasoning and tips
    final reasoning = _generateReasoning(program, goal, calorieAdjustment);
    final tips = _generateTips(program, goal);
    
    return NutritionRecommendation(
      dailyCalories: dailyCalories,
      proteinGrams: macros['protein']!,
      carbsGrams: macros['carbs']!,
      fatGrams: macros['fat']!,
      reasoning: reasoning,
      tips: tips,
    );
  }

  /// Determine nutrition goal from workout program type
  static NutritionGoal _determineGoalFromProgram(WorkoutProgram program) {
    switch (program.type) {
      case ProgramType.strength:
      case ProgramType.upperLower:
      case ProgramType.pushPullLegs:
        return NutritionGoal.muscleGain;
      case ProgramType.cardio:
      case ProgramType.hiit:
        return NutritionGoal.weightLoss;
      case ProgramType.fullBody:
        return NutritionGoal.maintenance;
      case ProgramType.yoga:
      case ProgramType.flexibility:
        return NutritionGoal.maintenance;
    }
  }

  /// Calculate calorie adjustment based on program and goal
  static double _calculateCalorieAdjustment(WorkoutProgram program, NutritionGoal goal) {
    double programMultiplier = 1.0;
    
    // Adjust based on program intensity
    switch (program.difficulty) {
      case DifficultyLevel.beginner:
        programMultiplier = 1.1;
        break;
      case DifficultyLevel.intermediate:
        programMultiplier = 1.2;
        break;
      case DifficultyLevel.advanced:
        programMultiplier = 1.3;
        break;
    }
    
    // Adjust based on workout frequency
    final frequencyMultiplier = 1.0 + (program.workoutsPerWeek * 0.05);
    
    // Adjust based on nutrition goal
    double goalAdjustment = 0;
    switch (goal) {
      case NutritionGoal.weightLoss:
        goalAdjustment = -300; // 300 calorie deficit
        break;
      case NutritionGoal.muscleGain:
        goalAdjustment = 300; // 300 calorie surplus
        break;
      case NutritionGoal.maintenance:
        goalAdjustment = 0;
        break;
      case NutritionGoal.endurance:
        goalAdjustment = 200; // Extra calories for endurance
        break;
      case NutritionGoal.strength:
        goalAdjustment = 150; // Moderate surplus for strength
        break;
    }
    
    return (_baseBMR * (programMultiplier * frequencyMultiplier - 1.0)) + goalAdjustment;
  }

  /// Calculate macronutrient distribution
  static Map<String, double> _calculateMacronutrients(
    int dailyCalories, 
    NutritionGoal goal, 
    WorkoutProgram program
  ) {
    double proteinRatio, carbsRatio, fatRatio;
    
    switch (goal) {
      case NutritionGoal.weightLoss:
        proteinRatio = 0.35; // Higher protein for muscle preservation
        carbsRatio = 0.35;
        fatRatio = 0.30;
        break;
      case NutritionGoal.muscleGain:
        proteinRatio = 0.30; // High protein for muscle building
        carbsRatio = 0.45; // Higher carbs for energy
        fatRatio = 0.25;
        break;
      case NutritionGoal.endurance:
        proteinRatio = 0.20;
        carbsRatio = 0.60; // Very high carbs for endurance
        fatRatio = 0.20;
        break;
      case NutritionGoal.strength:
        proteinRatio = 0.30;
        carbsRatio = 0.40;
        fatRatio = 0.30;
        break;
      case NutritionGoal.maintenance:
      default:
        proteinRatio = 0.25;
        carbsRatio = 0.45;
        fatRatio = 0.30;
        break;
    }
    
    // Adjust for program type
    if (program.type == ProgramType.cardio || program.type == ProgramType.hiit) {
      carbsRatio += 0.05; // More carbs for cardio
      fatRatio -= 0.05;
    }
    
    return {
      'protein': (dailyCalories * proteinRatio) / 4, // 4 calories per gram
      'carbs': (dailyCalories * carbsRatio) / 4,     // 4 calories per gram
      'fat': (dailyCalories * fatRatio) / 9,         // 9 calories per gram
    };
  }

  /// Generate reasoning for the recommendation
  static String _generateReasoning(WorkoutProgram program, NutritionGoal goal, double adjustment) {
    final adjustmentText = adjustment > 0 
        ? "increased by ${adjustment.round()} calories"
        : adjustment < 0 
            ? "reduced by ${(-adjustment).round()} calories"
            : "maintained at baseline";
    
    return "Based on your ${program.name} program (${program.difficultyDisplay}, ${program.frequencyDisplay}), "
           "your daily calorie target has been $adjustmentText to support ${goal.displayName.toLowerCase()}. "
           "This ${program.typeDisplay.toLowerCase()} program requires specific nutrition to optimize your results.";
  }

  /// Generate nutrition tips based on program and goal
  static List<String> _generateTips(WorkoutProgram program, NutritionGoal goal) {
    List<String> tips = [];
    
    // Goal-specific tips
    switch (goal) {
      case NutritionGoal.weightLoss:
        tips.addAll([
          "Focus on lean proteins to preserve muscle during weight loss",
          "Eat plenty of vegetables for nutrients and satiety",
          "Stay hydrated - aim for 8-10 glasses of water daily",
        ]);
        break;
      case NutritionGoal.muscleGain:
        tips.addAll([
          "Eat protein within 30 minutes after your workout",
          "Include complex carbs for sustained energy",
          "Don't skip meals - consistent nutrition supports muscle growth",
        ]);
        break;
      case NutritionGoal.endurance:
        tips.addAll([
          "Fuel up with carbs before longer workouts",
          "Consider electrolyte replacement during intense sessions",
          "Recovery nutrition is key - combine carbs and protein post-workout",
        ]);
        break;
      default:
        tips.addAll([
          "Balance your meals with protein, carbs, and healthy fats",
          "Time your nutrition around your workouts for best results",
          "Listen to your body and adjust portions as needed",
        ]);
    }
    
    // Program-specific tips
    if (program.type == ProgramType.strengthTraining) {
      tips.add("Prioritize protein intake on strength training days");
    } else if (program.type == ProgramType.cardio || program.type == ProgramType.hiit) {
      tips.add("Carbs are your friend for high-intensity cardio sessions");
    }
    
    // Add Nonni-specific encouragement
    tips.add("Remember Nonni, consistency with both training and nutrition will get you the best results! 💪");
    
    return tips;
  }

  /// Get quick nutrition summary for display
  static String getQuickSummary(NutritionRecommendation recommendation) {
    return "${recommendation.dailyCalories} cal • "
           "${recommendation.proteinGrams.round()}g protein • "
           "${recommendation.carbsGrams.round()}g carbs • "
           "${recommendation.fatGrams.round()}g fat";
  }
}
