import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/food_entry.dart';
import '../models/workout_entry.dart';
import '../models/exercise.dart';
import '../models/exercise_set.dart';
import '../models/exercise_log.dart';
import '../models/workout_program.dart';
import '../models/workout_session.dart';
import '../models/program_workout.dart';
import '../models/workout_exercise.dart';
import '../models/user_active_program.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'nonni_diet_tracker.db');
    return await openDatabase(
      path,
      version: 3,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    // Existing tables
    await db.execute('''
      CREATE TABLE food_entries(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        food TEXT NOT NULL,
        calories INTEGER NOT NULL,
        time INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE workout_entries(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        exercise TEXT NOT NULL,
        duration INTEGER NOT NULL,
        notes TEXT,
        date INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE user_settings(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        setting_key TEXT UNIQUE NOT NULL,
        setting_value TEXT NOT NULL
      )
    ''');

    // New workout system tables
    await db.execute('''
      CREATE TABLE exercises(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        type TEXT NOT NULL,
        muscle_groups TEXT NOT NULL,
        equipment TEXT NOT NULL,
        instructions TEXT NOT NULL,
        image_url TEXT,
        default_sets INTEGER DEFAULT 3,
        default_reps INTEGER DEFAULT 10,
        default_weight REAL DEFAULT 0.0,
        default_duration INTEGER DEFAULT 0,
        rest_time INTEGER DEFAULT 60
      )
    ''');

    await db.execute('''
      CREATE TABLE workout_programs(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        description TEXT NOT NULL,
        difficulty TEXT NOT NULL,
        type TEXT NOT NULL,
        duration_weeks INTEGER NOT NULL,
        workouts_per_week INTEGER NOT NULL,
        required_equipment TEXT,
        image_url TEXT,
        is_custom INTEGER DEFAULT 0,
        created_at INTEGER NOT NULL
      )
    ''');

    await db.execute('''
      CREATE TABLE workout_sessions(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        program_id INTEGER,
        name TEXT NOT NULL,
        description TEXT,
        scheduled_date INTEGER NOT NULL,
        start_time INTEGER,
        end_time INTEGER,
        status TEXT DEFAULT 'planned',
        notes TEXT,
        week INTEGER DEFAULT 1,
        day_of_week INTEGER DEFAULT 1,
        FOREIGN KEY (program_id) REFERENCES workout_programs (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE exercise_logs(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        workout_session_id INTEGER NOT NULL,
        exercise_id INTEGER NOT NULL,
        notes TEXT,
        start_time INTEGER NOT NULL,
        end_time INTEGER,
        completed INTEGER DEFAULT 0,
        FOREIGN KEY (workout_session_id) REFERENCES workout_sessions (id),
        FOREIGN KEY (exercise_id) REFERENCES exercises (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE exercise_sets(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        exercise_log_id INTEGER NOT NULL,
        set_number INTEGER NOT NULL,
        reps INTEGER NOT NULL,
        weight REAL DEFAULT 0.0,
        duration INTEGER DEFAULT 0,
        distance REAL DEFAULT 0.0,
        rest_time INTEGER DEFAULT 60,
        completed INTEGER DEFAULT 0,
        notes TEXT,
        timestamp INTEGER NOT NULL,
        FOREIGN KEY (exercise_log_id) REFERENCES exercise_logs (id)
      )
    ''');

    // Program-Exercise relationship tables
    await db.execute('''
      CREATE TABLE program_workouts(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        program_id INTEGER NOT NULL,
        name TEXT NOT NULL,
        description TEXT,
        day_of_week INTEGER NOT NULL,
        week_number INTEGER DEFAULT 1,
        order_index INTEGER DEFAULT 0,
        FOREIGN KEY (program_id) REFERENCES workout_programs (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE workout_exercises(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        workout_id INTEGER NOT NULL,
        exercise_id INTEGER NOT NULL,
        order_index INTEGER NOT NULL,
        sets INTEGER DEFAULT 3,
        reps INTEGER DEFAULT 10,
        weight REAL DEFAULT 0.0,
        duration INTEGER DEFAULT 0,
        rest_time INTEGER DEFAULT 60,
        notes TEXT,
        FOREIGN KEY (workout_id) REFERENCES program_workouts (id),
        FOREIGN KEY (exercise_id) REFERENCES exercises (id)
      )
    ''');

    await db.execute('''
      CREATE TABLE user_active_programs(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        program_id INTEGER NOT NULL,
        start_date INTEGER NOT NULL,
        current_week INTEGER DEFAULT 1,
        status TEXT DEFAULT 'active',
        notes TEXT,
        FOREIGN KEY (program_id) REFERENCES workout_programs (id)
      )
    ''');

    // Insert default calorie goal
    await db.insert('user_settings', {
      'setting_key': 'daily_calorie_goal',
      'setting_value': '2000'
    });

    // Insert default exercises
    await _insertDefaultExercises(db);

    // Insert default workout programs
    await _insertDefaultWorkoutPrograms(db);
  }

  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < 2) {
      // Add new tables for workout system
      await db.execute('''
        CREATE TABLE exercises(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT NOT NULL,
          type TEXT NOT NULL,
          muscle_groups TEXT NOT NULL,
          equipment TEXT NOT NULL,
          instructions TEXT NOT NULL,
          image_url TEXT,
          default_sets INTEGER DEFAULT 3,
          default_reps INTEGER DEFAULT 10,
          default_weight REAL DEFAULT 0.0,
          default_duration INTEGER DEFAULT 0,
          rest_time INTEGER DEFAULT 60
        )
      ''');

      await db.execute('''
        CREATE TABLE workout_programs(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          description TEXT NOT NULL,
          difficulty TEXT NOT NULL,
          type TEXT NOT NULL,
          duration_weeks INTEGER NOT NULL,
          workouts_per_week INTEGER NOT NULL,
          required_equipment TEXT,
          image_url TEXT,
          is_custom INTEGER DEFAULT 0,
          created_at INTEGER NOT NULL
        )
      ''');

      await db.execute('''
        CREATE TABLE workout_sessions(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          program_id INTEGER,
          name TEXT NOT NULL,
          description TEXT,
          scheduled_date INTEGER NOT NULL,
          start_time INTEGER,
          end_time INTEGER,
          status TEXT DEFAULT 'planned',
          notes TEXT,
          week INTEGER DEFAULT 1,
          day_of_week INTEGER DEFAULT 1,
          FOREIGN KEY (program_id) REFERENCES workout_programs (id)
        )
      ''');

      await db.execute('''
        CREATE TABLE exercise_logs(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          workout_session_id INTEGER NOT NULL,
          exercise_id INTEGER NOT NULL,
          notes TEXT,
          start_time INTEGER NOT NULL,
          end_time INTEGER,
          completed INTEGER DEFAULT 0,
          FOREIGN KEY (workout_session_id) REFERENCES workout_sessions (id),
          FOREIGN KEY (exercise_id) REFERENCES exercises (id)
        )
      ''');

      await db.execute('''
        CREATE TABLE exercise_sets(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          exercise_log_id INTEGER NOT NULL,
          set_number INTEGER NOT NULL,
          reps INTEGER NOT NULL,
          weight REAL DEFAULT 0.0,
          duration INTEGER DEFAULT 0,
          distance REAL DEFAULT 0.0,
          rest_time INTEGER DEFAULT 60,
          completed INTEGER DEFAULT 0,
          notes TEXT,
          timestamp INTEGER NOT NULL,
          FOREIGN KEY (exercise_log_id) REFERENCES exercise_logs (id)
        )
      ''');

      // Program-Exercise relationship tables
      await db.execute('''
        CREATE TABLE program_workouts(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          program_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          description TEXT,
          day_of_week INTEGER NOT NULL,
          week_number INTEGER DEFAULT 1,
          order_index INTEGER DEFAULT 0,
          FOREIGN KEY (program_id) REFERENCES workout_programs (id)
        )
      ''');

      await db.execute('''
        CREATE TABLE workout_exercises(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          workout_id INTEGER NOT NULL,
          exercise_id INTEGER NOT NULL,
          order_index INTEGER NOT NULL,
          sets INTEGER DEFAULT 3,
          reps INTEGER DEFAULT 10,
          weight REAL DEFAULT 0.0,
          duration INTEGER DEFAULT 0,
          rest_time INTEGER DEFAULT 60,
          notes TEXT,
          FOREIGN KEY (workout_id) REFERENCES program_workouts (id),
          FOREIGN KEY (exercise_id) REFERENCES exercises (id)
        )
      ''');

      await db.execute('''
        CREATE TABLE user_active_programs(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          program_id INTEGER NOT NULL,
          start_date INTEGER NOT NULL,
          current_week INTEGER DEFAULT 1,
          status TEXT DEFAULT 'active',
          notes TEXT,
          FOREIGN KEY (program_id) REFERENCES workout_programs (id)
        )
      ''');

      await _insertDefaultExercises(db);
      await _insertDefaultWorkoutPrograms(db);
    }

    if (oldVersion < 3) {
      // Add program-exercise relationship tables
      await db.execute('''
        CREATE TABLE IF NOT EXISTS program_workouts(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          program_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          description TEXT,
          day_of_week INTEGER NOT NULL,
          week_number INTEGER DEFAULT 1,
          order_index INTEGER DEFAULT 0,
          FOREIGN KEY (program_id) REFERENCES workout_programs (id)
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS workout_exercises(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          workout_id INTEGER NOT NULL,
          exercise_id INTEGER NOT NULL,
          order_index INTEGER NOT NULL,
          sets INTEGER DEFAULT 3,
          reps INTEGER DEFAULT 10,
          weight REAL DEFAULT 0.0,
          duration INTEGER DEFAULT 0,
          rest_time INTEGER DEFAULT 60,
          notes TEXT,
          FOREIGN KEY (workout_id) REFERENCES program_workouts (id),
          FOREIGN KEY (exercise_id) REFERENCES exercises (id)
        )
      ''');

      await db.execute('''
        CREATE TABLE IF NOT EXISTS user_active_programs(
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          program_id INTEGER NOT NULL,
          start_date INTEGER NOT NULL,
          current_week INTEGER DEFAULT 1,
          status TEXT DEFAULT 'active',
          notes TEXT,
          FOREIGN KEY (program_id) REFERENCES workout_programs (id)
        )
      ''');
    }
  }

  Future<void> _insertDefaultExercises(Database db) async {
    final exercises = [
      {
        'name': 'Push-ups',
        'description': 'Classic bodyweight chest exercise',
        'type': 'strength',
        'muscle_groups': 'chest,triceps,shoulders',
        'equipment': 'none',
        'instructions': '1. Start in plank position\n2. Lower body until chest nearly touches floor\n3. Push back up to starting position',
        'default_sets': 3,
        'default_reps': 10,
        'rest_time': 60,
      },
      {
        'name': 'Squats',
        'description': 'Fundamental lower body exercise',
        'type': 'strength',
        'muscle_groups': 'legs,glutes',
        'equipment': 'none',
        'instructions': '1. Stand with feet shoulder-width apart\n2. Lower body as if sitting back into chair\n3. Return to standing position',
        'default_sets': 3,
        'default_reps': 15,
        'rest_time': 60,
      },
      {
        'name': 'Plank',
        'description': 'Core strengthening exercise',
        'type': 'strength',
        'muscle_groups': 'core',
        'equipment': 'none',
        'instructions': '1. Start in push-up position\n2. Hold body in straight line\n3. Engage core throughout',
        'default_sets': 3,
        'default_reps': 1,
        'default_duration': 30,
        'rest_time': 60,
      },
      {
        'name': 'Lunges',
        'description': 'Single-leg strength exercise',
        'type': 'strength',
        'muscle_groups': 'legs,glutes',
        'equipment': 'none',
        'instructions': '1. Step forward into lunge position\n2. Lower back knee toward ground\n3. Return to starting position',
        'default_sets': 3,
        'default_reps': 12,
        'rest_time': 60,
      },
      {
        'name': 'Jumping Jacks',
        'description': 'Full-body cardio exercise',
        'type': 'cardio',
        'muscle_groups': 'fullBody,cardio',
        'equipment': 'none',
        'instructions': '1. Start with feet together, arms at sides\n2. Jump feet apart while raising arms overhead\n3. Return to starting position',
        'default_sets': 3,
        'default_reps': 20,
        'rest_time': 30,
      },
    ];

    for (final exercise in exercises) {
      await db.insert('exercises', exercise);
    }
  }

  Future<void> _insertDefaultWorkoutPrograms(Database db) async {
    final programs = [
      {
        'name': "Nonni's Beginner Journey",
        'description': 'Perfect starting point for building healthy habits',
        'difficulty': 'beginner',
        'type': 'fullBody',
        'duration_weeks': 4,
        'workouts_per_week': 3,
        'required_equipment': '',
        'is_custom': 0,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      },
      {
        'name': "Nonni's Strength Builder",
        'description': 'Build strength and confidence progressively',
        'difficulty': 'intermediate',
        'type': 'strength',
        'duration_weeks': 6,
        'workouts_per_week': 4,
        'required_equipment': 'dumbbells',
        'is_custom': 0,
        'created_at': DateTime.now().millisecondsSinceEpoch,
      },
    ];

    for (final program in programs) {
      await db.insert('workout_programs', program);
    }
  }

  // Food Entry Methods
  Future<int> insertFoodEntry(FoodEntry entry) async {
    final db = await database;
    return await db.insert('food_entries', entry.toMap());
  }

  Future<List<FoodEntry>> getFoodEntriesForDate(DateTime date) async {
    final db = await database;
    final startOfDay = DateTime(date.year, date.month, date.day);
    final endOfDay = startOfDay.add(const Duration(days: 1));
    
    final List<Map<String, dynamic>> maps = await db.query(
      'food_entries',
      where: 'time >= ? AND time < ?',
      whereArgs: [startOfDay.millisecondsSinceEpoch, endOfDay.millisecondsSinceEpoch],
      orderBy: 'time DESC',
    );

    return List.generate(maps.length, (i) => FoodEntry.fromMap(maps[i]));
  }

  Future<int> deleteFoodEntry(int id) async {
    final db = await database;
    return await db.delete('food_entries', where: 'id = ?', whereArgs: [id]);
  }

  // Workout Entry Methods
  Future<int> insertWorkoutEntry(WorkoutEntry entry) async {
    final db = await database;
    return await db.insert('workout_entries', entry.toMap());
  }

  Future<List<WorkoutEntry>> getAllWorkoutEntries() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'workout_entries',
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) => WorkoutEntry.fromMap(maps[i]));
  }

  Future<List<WorkoutEntry>> getWorkoutEntriesForWeek(DateTime date) async {
    final db = await database;
    final weekStart = date.subtract(Duration(days: date.weekday - 1));
    final weekEnd = weekStart.add(const Duration(days: 7));
    
    final List<Map<String, dynamic>> maps = await db.query(
      'workout_entries',
      where: 'date >= ? AND date < ?',
      whereArgs: [weekStart.millisecondsSinceEpoch, weekEnd.millisecondsSinceEpoch],
      orderBy: 'date DESC',
    );

    return List.generate(maps.length, (i) => WorkoutEntry.fromMap(maps[i]));
  }

  Future<int> deleteWorkoutEntry(int id) async {
    final db = await database;
    return await db.delete('workout_entries', where: 'id = ?', whereArgs: [id]);
  }

  // Settings Methods
  Future<String?> getSetting(String key) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_settings',
      where: 'setting_key = ?',
      whereArgs: [key],
    );

    if (maps.isNotEmpty) {
      return maps.first['setting_value'];
    }
    return null;
  }

  Future<void> setSetting(String key, String value) async {
    final db = await database;
    await db.insert(
      'user_settings',
      {'setting_key': key, 'setting_value': value},
      conflictAlgorithm: ConflictAlgorithm.replace,
    );
  }

  Future<int> getDailyCalorieGoal() async {
    final goal = await getSetting('daily_calorie_goal');
    return int.tryParse(goal ?? '2000') ?? 2000;
  }

  Future<void> setDailyCalorieGoal(int goal) async {
    await setSetting('daily_calorie_goal', goal.toString());
  }

  // Program-based nutrition methods
  Future<void> setProgramBasedNutrition(bool enabled) async {
    await setSetting('program_based_nutrition', enabled.toString());
  }

  Future<bool> isProgramBasedNutritionEnabled() async {
    final setting = await getSetting('program_based_nutrition');
    return setting?.toLowerCase() == 'true';
  }

  Future<void> setNutritionGoal(String goal) async {
    await setSetting('nutrition_goal', goal);
  }

  Future<String?> getNutritionGoal() async {
    return await getSetting('nutrition_goal');
  }

  Future<void> saveNutritionRecommendation(Map<String, dynamic> recommendation) async {
    await setSetting('current_nutrition_recommendation',
        recommendation.entries.map((e) => '${e.key}:${e.value}').join('|'));
  }

  Future<Map<String, dynamic>?> getCurrentNutritionRecommendation() async {
    final setting = await getSetting('current_nutrition_recommendation');
    if (setting == null) return null;

    final Map<String, dynamic> recommendation = {};
    for (final pair in setting.split('|')) {
      final parts = pair.split(':');
      if (parts.length == 2) {
        final key = parts[0];
        final value = parts[1];

        // Convert to appropriate types
        if (key.contains('calories') || key.contains('grams')) {
          recommendation[key] = double.tryParse(value) ?? 0.0;
        } else {
          recommendation[key] = value;
        }
      }
    }
    return recommendation;
  }

  // Exercise Methods
  Future<List<Exercise>> getAllExercises() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('exercises');
    return List.generate(maps.length, (i) => Exercise.fromMap(maps[i]));
  }

  Future<Exercise?> getExerciseById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'exercises',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return Exercise.fromMap(maps.first);
    }
    return null;
  }

  Future<List<Exercise>> getExercisesByMuscleGroup(MuscleGroup muscleGroup) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'exercises',
      where: 'muscle_groups LIKE ?',
      whereArgs: ['%${muscleGroup.name}%'],
    );
    return List.generate(maps.length, (i) => Exercise.fromMap(maps[i]));
  }

  Future<int> insertExercise(Exercise exercise) async {
    final db = await database;
    return await db.insert('exercises', exercise.toMap());
  }

  // Workout Program Methods
  Future<List<WorkoutProgram>> getAllWorkoutPrograms() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query('workout_programs');
    return List.generate(maps.length, (i) => WorkoutProgram.fromMap(maps[i]));
  }

  Future<WorkoutProgram?> getWorkoutProgramById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'workout_programs',
      where: 'id = ?',
      whereArgs: [id],
    );
    if (maps.isNotEmpty) {
      return WorkoutProgram.fromMap(maps.first);
    }
    return null;
  }

  // Workout Session Methods
  Future<int> insertWorkoutSession(WorkoutSession session) async {
    final db = await database;
    return await db.insert('workout_sessions', session.toMap());
  }

  Future<List<WorkoutSession>> getWorkoutSessionsForProgram(int programId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'workout_sessions',
      where: 'program_id = ?',
      whereArgs: [programId],
      orderBy: 'scheduled_date ASC',
    );
    return List.generate(maps.length, (i) => WorkoutSession.fromMap(maps[i]));
  }

  Future<List<WorkoutSession>> getRecentWorkoutSessions({int limit = 10}) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'workout_sessions',
      orderBy: 'scheduled_date DESC',
      limit: limit,
    );
    return List.generate(maps.length, (i) => WorkoutSession.fromMap(maps[i]));
  }

  Future<void> updateWorkoutSession(WorkoutSession session) async {
    final db = await database;
    await db.update(
      'workout_sessions',
      session.toMap(),
      where: 'id = ?',
      whereArgs: [session.id],
    );
  }

  // Exercise Log Methods
  Future<int> insertExerciseLog(ExerciseLog log) async {
    final db = await database;
    return await db.insert('exercise_logs', log.toMap());
  }

  Future<List<ExerciseLog>> getExerciseLogsForSession(int sessionId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'exercise_logs',
      where: 'workout_session_id = ?',
      whereArgs: [sessionId],
      orderBy: 'start_time ASC',
    );
    return List.generate(maps.length, (i) => ExerciseLog.fromMap(maps[i]));
  }

  // Exercise Set Methods
  Future<int> insertExerciseSet(ExerciseSet set) async {
    final db = await database;
    return await db.insert('exercise_sets', set.toMap());
  }

  Future<List<ExerciseSet>> getExerciseSetsForLog(int logId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'exercise_sets',
      where: 'exercise_log_id = ?',
      whereArgs: [logId],
      orderBy: 'set_number ASC',
    );
    return List.generate(maps.length, (i) => ExerciseSet.fromMap(maps[i]));
  }

  Future<void> updateExerciseSet(ExerciseSet set) async {
    final db = await database;
    await db.update(
      'exercise_sets',
      set.toMap(),
      where: 'id = ?',
      whereArgs: [set.id],
    );
  }

  // Program Workout Methods
  Future<int> insertProgramWorkout(ProgramWorkout workout) async {
    final db = await database;
    return await db.insert('program_workouts', workout.toMap());
  }

  Future<List<ProgramWorkout>> getProgramWorkouts(int programId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'program_workouts',
      where: 'program_id = ?',
      whereArgs: [programId],
      orderBy: 'week_number ASC, day_of_week ASC, order_index ASC',
    );
    return List.generate(maps.length, (i) => ProgramWorkout.fromMap(maps[i]));
  }

  // Workout Exercise Methods
  Future<int> insertWorkoutExercise(WorkoutExercise workoutExercise) async {
    final db = await database;
    return await db.insert('workout_exercises', workoutExercise.toMap());
  }

  Future<List<WorkoutExercise>> getWorkoutExercises(int workoutId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'workout_exercises',
      where: 'workout_id = ?',
      whereArgs: [workoutId],
      orderBy: 'order_index ASC',
    );
    return List.generate(maps.length, (i) => WorkoutExercise.fromMap(maps[i]));
  }

  // User Active Program Methods
  Future<int> insertUserActiveProgram(UserActiveProgram activeProgram) async {
    final db = await database;
    return await db.insert('user_active_programs', activeProgram.toMap());
  }

  Future<UserActiveProgram?> getCurrentActiveProgram() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_active_programs',
      where: 'status = ?',
      whereArgs: ['active'],
      orderBy: 'start_date DESC',
      limit: 1,
    );
    if (maps.isNotEmpty) {
      return UserActiveProgram.fromMap(maps.first);
    }
    return null;
  }

  Future<List<UserActiveProgram>> getAllUserPrograms() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'user_active_programs',
      orderBy: 'start_date DESC',
    );
    return List.generate(maps.length, (i) => UserActiveProgram.fromMap(maps[i]));
  }

  Future<void> updateUserActiveProgram(UserActiveProgram activeProgram) async {
    final db = await database;
    await db.update(
      'user_active_programs',
      activeProgram.toMap(),
      where: 'id = ?',
      whereArgs: [activeProgram.id],
    );
  }
}
