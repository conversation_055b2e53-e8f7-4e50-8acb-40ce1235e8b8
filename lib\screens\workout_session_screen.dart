import 'package:flutter/material.dart';
import '../models/workout_session.dart';
import '../models/exercise_log.dart';
import '../services/database_service.dart';

class WorkoutSessionScreen extends StatefulWidget {
  final WorkoutSession session;

  const WorkoutSessionScreen({
    super.key,
    required this.session,
  });

  @override
  State<WorkoutSessionScreen> createState() => _WorkoutSessionScreenState();
}

class _WorkoutSessionScreenState extends State<WorkoutSessionScreen> {
  late WorkoutSession _session;
  List<ExerciseLog> _exerciseLogs = [];
  final DatabaseService _databaseService = DatabaseService();
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _session = widget.session;
    _loadExerciseLogs();
  }

  Future<void> _loadExerciseLogs() async {
    if (_session.id != null) {
      try {
        final logs = await _databaseService.getExerciseLogsForSession(_session.id!);
        setState(() {
          _exerciseLogs = logs;
          _isLoading = false;
        });
      } catch (e) {
        setState(() {
          _isLoading = false;
        });
      }
    } else {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_session.name),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          if (_session.canStart)
            IconButton(
              icon: const Icon(Icons.play_arrow),
              onPressed: _startWorkout,
            ),
          if (_session.canComplete)
            IconButton(
              icon: const Icon(Icons.check),
              onPressed: _completeWorkout,
            ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Session info card
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.fitness_center,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Workout Session',
                                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text('Status: ${_session.statusDisplay}'),
                          if (_session.description.isNotEmpty)
                            Text('Description: ${_session.description}'),
                          if (_session.duration != null)
                            Text('Duration: ${_session.durationDisplay}'),
                        ],
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  
                  // Exercise logs
                  Text(
                    'Exercises',
                    style: Theme.of(context).textTheme.titleLarge,
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: _exerciseLogs.isEmpty
                        ? Center(
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.fitness_center,
                                  size: 64,
                                  color: Colors.grey[300],
                                ),
                                const SizedBox(height: 16),
                                const Text(
                                  'No exercises added yet',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                ElevatedButton(
                                  onPressed: _addExercise,
                                  child: const Text('Add Exercise'),
                                ),
                              ],
                            ),
                          )
                        : ListView.builder(
                            itemCount: _exerciseLogs.length,
                            itemBuilder: (context, index) {
                              final log = _exerciseLogs[index];
                              return Card(
                                child: ListTile(
                                  leading: Icon(
                                    log.completed ? Icons.check_circle : Icons.radio_button_unchecked,
                                    color: log.completed ? Colors.green : Colors.grey,
                                  ),
                                  title: Text(log.exercise?.name ?? 'Unknown Exercise'),
                                  subtitle: Text(log.progressSummary),
                                  trailing: IconButton(
                                    icon: const Icon(Icons.edit),
                                    onPressed: () => _editExerciseLog(log),
                                  ),
                                  onTap: () => _editExerciseLog(log),
                                ),
                              );
                            },
                          ),
                  ),
                ],
              ),
            ),
      floatingActionButton: _session.isActive
          ? FloatingActionButton(
              onPressed: _addExercise,
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Future<void> _startWorkout() async {
    try {
      final updatedSession = _session.copyWith(
        status: SessionStatus.inProgress,
        startTime: DateTime.now(),
      );
      
      await _databaseService.updateWorkoutSession(updatedSession);
      
      setState(() {
        _session = updatedSession;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Workout started! 💪'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error starting workout: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _completeWorkout() async {
    try {
      final updatedSession = _session.copyWith(
        status: SessionStatus.completed,
        endTime: DateTime.now(),
      );
      
      await _databaseService.updateWorkoutSession(updatedSession);
      
      setState(() {
        _session = updatedSession;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: const Text('Workout completed! Great job! 🎉'),
            backgroundColor: Theme.of(context).colorScheme.primary,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error completing workout: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _addExercise() {
    // TODO: Implement exercise selection and addition
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Exercise selection coming soon!'),
      ),
    );
  }

  void _editExerciseLog(ExerciseLog log) {
    // TODO: Implement exercise log editing with sets/reps/weights
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Exercise editing coming soon!'),
      ),
    );
  }
}
