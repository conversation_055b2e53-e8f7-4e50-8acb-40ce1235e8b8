class WorkoutExercise {
  final int? id;
  final int workoutId;
  final int exerciseId;
  final int orderIndex;
  final int sets;
  final int reps;
  final double weight;
  final int duration; // in seconds
  final int restTime; // in seconds
  final String notes;

  WorkoutExercise({
    this.id,
    required this.workoutId,
    required this.exerciseId,
    required this.orderIndex,
    this.sets = 3,
    this.reps = 10,
    this.weight = 0.0,
    this.duration = 0,
    this.restTime = 60,
    this.notes = '',
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workout_id': workoutId,
      'exercise_id': exerciseId,
      'order_index': orderIndex,
      'sets': sets,
      'reps': reps,
      'weight': weight,
      'duration': duration,
      'rest_time': restTime,
      'notes': notes,
    };
  }

  factory WorkoutExercise.fromMap(Map<String, dynamic> map) {
    return WorkoutExercise(
      id: map['id'],
      workoutId: map['workout_id'],
      exerciseId: map['exercise_id'],
      orderIndex: map['order_index'],
      sets: map['sets'] ?? 3,
      reps: map['reps'] ?? 10,
      weight: map['weight']?.toDouble() ?? 0.0,
      duration: map['duration'] ?? 0,
      restTime: map['rest_time'] ?? 60,
      notes: map['notes'] ?? '',
    );
  }

  String get setsRepsDisplay {
    if (duration > 0) {
      final minutes = duration ~/ 60;
      final seconds = duration % 60;
      if (minutes > 0) {
        return '$sets sets × ${minutes}m ${seconds}s';
      } else {
        return '$sets sets × ${seconds}s';
      }
    } else {
      return '$sets sets × $reps reps';
    }
  }

  String get weightDisplay {
    if (weight > 0) {
      return '${weight.toStringAsFixed(weight == weight.roundToDouble() ? 0 : 1)}kg';
    }
    return 'Bodyweight';
  }

  String get restTimeDisplay {
    final minutes = restTime ~/ 60;
    final seconds = restTime % 60;
    if (minutes > 0) {
      return '${minutes}m ${seconds}s rest';
    } else {
      return '${seconds}s rest';
    }
  }

  WorkoutExercise copyWith({
    int? id,
    int? workoutId,
    int? exerciseId,
    int? orderIndex,
    int? sets,
    int? reps,
    double? weight,
    int? duration,
    int? restTime,
    String? notes,
  }) {
    return WorkoutExercise(
      id: id ?? this.id,
      workoutId: workoutId ?? this.workoutId,
      exerciseId: exerciseId ?? this.exerciseId,
      orderIndex: orderIndex ?? this.orderIndex,
      sets: sets ?? this.sets,
      reps: reps ?? this.reps,
      weight: weight ?? this.weight,
      duration: duration ?? this.duration,
      restTime: restTime ?? this.restTime,
      notes: notes ?? this.notes,
    );
  }
}
