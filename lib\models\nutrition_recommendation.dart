class NutritionRecommendation {
  final int dailyCalories;
  final double proteinGrams;
  final double carbsGrams;
  final double fatGrams;
  final String reasoning;
  final List<String> tips;

  const NutritionRecommendation({
    required this.dailyCalories,
    required this.proteinGrams,
    required this.carbsGrams,
    required this.fatGrams,
    required this.reasoning,
    required this.tips,
  });

  // Calculate macronutrient percentages
  double get proteinPercentage => (proteinGrams * 4) / dailyCalories * 100;
  double get carbsPercentage => (carbsGrams * 4) / dailyCalories * 100;
  double get fatPercentage => (fatGrams * 9) / dailyCalories * 100;

  // Calculate calories from each macronutrient
  int get proteinCalories => (proteinGrams * 4).round();
  int get carbsCalories => (carbsGrams * 4).round();
  int get fatCalories => (fatGrams * 9).round();

  Map<String, dynamic> toMap() {
    return {
      'daily_calories': dailyCalories,
      'protein_grams': proteinGrams,
      'carbs_grams': carbsGrams,
      'fat_grams': fatGrams,
      'reasoning': reasoning,
      'tips': tips.join('|'),
    };
  }

  factory NutritionRecommendation.fromMap(Map<String, dynamic> map) {
    return NutritionRecommendation(
      dailyCalories: map['daily_calories']?.toInt() ?? 0,
      proteinGrams: map['protein_grams']?.toDouble() ?? 0.0,
      carbsGrams: map['carbs_grams']?.toDouble() ?? 0.0,
      fatGrams: map['fat_grams']?.toDouble() ?? 0.0,
      reasoning: map['reasoning'] ?? '',
      tips: (map['tips'] as String?)?.split('|') ?? [],
    );
  }

  @override
  String toString() {
    return 'NutritionRecommendation(dailyCalories: $dailyCalories, protein: ${proteinGrams}g, carbs: ${carbsGrams}g, fat: ${fatGrams}g)';
  }
}

enum NutritionGoal {
  weightLoss,
  muscleGain,
  maintenance,
  endurance,
  strength,
}

extension NutritionGoalExtension on NutritionGoal {
  String get displayName {
    switch (this) {
      case NutritionGoal.weightLoss:
        return 'Weight Loss';
      case NutritionGoal.muscleGain:
        return 'Muscle Gain';
      case NutritionGoal.maintenance:
        return 'Maintenance';
      case NutritionGoal.endurance:
        return 'Endurance';
      case NutritionGoal.strength:
        return 'Strength';
    }
  }

  String get description {
    switch (this) {
      case NutritionGoal.weightLoss:
        return 'Caloric deficit for fat loss while preserving muscle';
      case NutritionGoal.muscleGain:
        return 'Caloric surplus with high protein for muscle building';
      case NutritionGoal.maintenance:
        return 'Balanced nutrition to maintain current weight';
      case NutritionGoal.endurance:
        return 'Higher carbs for sustained energy during cardio';
      case NutritionGoal.strength:
        return 'Adequate protein and calories for strength gains';
    }
  }
}
