class WorkoutEntry {
  final int? id;
  final String exercise;
  final int duration;
  final String notes;
  final DateTime date;

  WorkoutEntry({
    this.id,
    required this.exercise,
    required this.duration,
    required this.notes,
    required this.date,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'exercise': exercise,
      'duration': duration,
      'notes': notes,
      'date': date.millisecondsSinceEpoch,
    };
  }

  factory WorkoutEntry.fromMap(Map<String, dynamic> map) {
    return WorkoutEntry(
      id: map['id'],
      exercise: map['exercise'],
      duration: map['duration'],
      notes: map['notes'] ?? '',
      date: DateTime.fromMillisecondsSinceEpoch(map['date']),
    );
  }

  WorkoutEntry copyWith({
    int? id,
    String? exercise,
    int? duration,
    String? notes,
    DateTime? date,
  }) {
    return WorkoutEntry(
      id: id ?? this.id,
      exercise: exercise ?? this.exercise,
      duration: duration ?? this.duration,
      notes: notes ?? this.notes,
      date: date ?? this.date,
    );
  }
}
