class Product {
  final String barcode;
  final String name;
  final String brand;
  final double caloriesPer100g;
  final double proteinPer100g;
  final double carbsPer100g;
  final double fatPer100g;
  final String imageUrl;
  final String servingSize;

  Product({
    required this.barcode,
    required this.name,
    required this.brand,
    required this.caloriesPer100g,
    required this.proteinPer100g,
    required this.carbsPer100g,
    required this.fatPer100g,
    required this.imageUrl,
    required this.servingSize,
  });

  factory Product.fromOpenFoodFacts(Map<String, dynamic> json, String barcode) {
    final product = json['product'] ?? {};
    final nutriments = product['nutriments'] ?? {};
    
    return Product(
      barcode: barcode,
      name: product['product_name'] ?? 'Unknown Product',
      brand: product['brands'] ?? 'Unknown Brand',
      caloriesPer100g: _parseDouble(nutriments['energy-kcal_100g']),
      proteinPer100g: _parseDouble(nutriments['proteins_100g']),
      carbsPer100g: _parseDouble(nutriments['carbohydrates_100g']),
      fatPer100g: _parseDouble(nutriments['fat_100g']),
      imageUrl: product['image_url'] ?? '',
      servingSize: product['serving_size'] ?? '100g',
    );
  }

  static double _parseDouble(dynamic value) {
    if (value == null) return 0.0;
    if (value is double) return value;
    if (value is int) return value.toDouble();
    if (value is String) return double.tryParse(value) ?? 0.0;
    return 0.0;
  }

  int calculateCaloriesForAmount(double grams) {
    return ((caloriesPer100g * grams) / 100).round();
  }

  String get displayName {
    if (brand.isNotEmpty && brand != 'Unknown Brand') {
      return '$brand $name';
    }
    return name;
  }

  String get nutritionSummary {
    return 'Per 100g: ${caloriesPer100g.toStringAsFixed(0)} kcal, '
           'Protein: ${proteinPer100g.toStringAsFixed(1)}g, '
           'Carbs: ${carbsPer100g.toStringAsFixed(1)}g, '
           'Fat: ${fatPer100g.toStringAsFixed(1)}g';
  }
}
