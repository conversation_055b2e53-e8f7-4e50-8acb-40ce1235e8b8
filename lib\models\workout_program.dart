import 'exercise.dart';

enum DifficultyLevel {
  beginner,
  intermediate,
  advanced,
}

enum ProgramType {
  strength,
  cardio,
  hiit,
  yoga,
  fullBody,
  upperLower,
  pushPullLegs,
  flexibility,
}

class WorkoutProgram {
  final int? id;
  final String name;
  final String description;
  final DifficultyLevel difficulty;
  final ProgramType type;
  final int durationWeeks;
  final int workoutsPerWeek;
  final List<Equipment> requiredEquipment;
  final String imageUrl;
  final bool isCustom;
  final DateTime createdAt;

  WorkoutProgram({
    this.id,
    required this.name,
    required this.description,
    required this.difficulty,
    required this.type,
    required this.durationWeeks,
    required this.workoutsPerWeek,
    this.requiredEquipment = const [],
    this.imageUrl = '',
    this.isCustom = false,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'difficulty': difficulty.name,
      'type': type.name,
      'duration_weeks': durationWeeks,
      'workouts_per_week': workoutsPerWeek,
      'required_equipment': requiredEquipment.map((eq) => eq.name).join(','),
      'image_url': imageUrl,
      'is_custom': isCustom ? 1 : 0,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  factory WorkoutProgram.fromMap(Map<String, dynamic> map) {
    return WorkoutProgram(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      difficulty: DifficultyLevel.values.firstWhere(
        (d) => d.name == map['difficulty'],
        orElse: () => DifficultyLevel.beginner,
      ),
      type: ProgramType.values.firstWhere(
        (t) => t.name == map['type'],
        orElse: () => ProgramType.fullBody,
      ),
      durationWeeks: map['duration_weeks'],
      workoutsPerWeek: map['workouts_per_week'],
      requiredEquipment: (map['required_equipment'] as String?)
              ?.split(',')
              .where((eq) => eq.isNotEmpty)
              .map((eq) => Equipment.values.firstWhere(
                    (e) => e.name == eq,
                    orElse: () => Equipment.none,
                  ))
              .toList() ??
          [],
      imageUrl: map['image_url'] ?? '',
      isCustom: map['is_custom'] == 1,
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
    );
  }

  String get difficultyDisplay {
    switch (difficulty) {
      case DifficultyLevel.beginner:
        return 'Beginner';
      case DifficultyLevel.intermediate:
        return 'Intermediate';
      case DifficultyLevel.advanced:
        return 'Advanced';
    }
  }

  String get typeDisplay {
    switch (type) {
      case ProgramType.strength:
        return 'Strength Training';
      case ProgramType.cardio:
        return 'Cardio';
      case ProgramType.hiit:
        return 'HIIT';
      case ProgramType.yoga:
        return 'Yoga';
      case ProgramType.fullBody:
        return 'Full Body';
      case ProgramType.upperLower:
        return 'Upper/Lower Split';
      case ProgramType.pushPullLegs:
        return 'Push/Pull/Legs';
      case ProgramType.flexibility:
        return 'Flexibility & Mobility';
    }
  }

  String get equipmentDisplay {
    if (requiredEquipment.isEmpty) return 'No equipment needed';
    return requiredEquipment
        .map((eq) => eq.name.replaceAll('_', ' '))
        .join(', ');
  }

  String get durationDisplay {
    return '$durationWeeks week${durationWeeks > 1 ? 's' : ''}';
  }

  String get frequencyDisplay {
    return '$workoutsPerWeek workout${workoutsPerWeek > 1 ? 's' : ''} per week';
  }
}
