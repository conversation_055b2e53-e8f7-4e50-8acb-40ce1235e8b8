import '../models/workout_program.dart';
import '../models/program_workout.dart';
import '../models/workout_exercise.dart';
import '../models/exercise.dart';
import '../services/database_service.dart';

class ProgramTemplateService {
  static final ProgramTemplateService _instance = ProgramTemplateService._internal();
  factory ProgramTemplateService() => _instance;
  ProgramTemplateService._internal();

  final DatabaseService _databaseService = DatabaseService();

  /// Initialize program templates with exercises
  Future<void> initializeProgramTemplates() async {
    // Check if templates are already created
    final existingWorkouts = await _databaseService.getProgramWorkouts(1);
    if (existingWorkouts.isNotEmpty) {
      return; // Already initialized
    }

    // Get all programs and exercises
    final programs = await _databaseService.getAllWorkoutPrograms();
    final exercises = await _databaseService.getAllExercises();

    for (final program in programs) {
      await _createProgramTemplate(program, exercises);
    }
  }

  Future<void> _createProgramTemplate(WorkoutProgram program, List<Exercise> exercises) async {
    switch (program.type) {
      case ProgramType.fullBody:
        await _createFullBodyTemplate(program, exercises);
        break;
      case ProgramType.upperLower:
        await _createUpperLowerTemplate(program, exercises);
        break;
      case ProgramType.pushPullLegs:
        await _createPushPullLegsTemplate(program, exercises);
        break;
      case ProgramType.strength:
        await _createStrengthTemplate(program, exercises);
        break;
      case ProgramType.cardio:
        await _createCardioTemplate(program, exercises);
        break;
      case ProgramType.hiit:
        await _createHIITTemplate(program, exercises);
        break;
      case ProgramType.yoga:
        await _createYogaTemplate(program, exercises);
        break;
      case ProgramType.flexibility:
        await _createFlexibilityTemplate(program, exercises);
        break;
    }
  }

  Future<void> _createFullBodyTemplate(WorkoutProgram program, List<Exercise> exercises) async {
    if (program.id == null) return;

    // Create 3 full body workouts per week
    for (int week = 1; week <= program.durationWeeks; week++) {
      final workoutDays = [1, 3, 5]; // Monday, Wednesday, Friday
      
      for (int i = 0; i < workoutDays.length; i++) {
        final workout = ProgramWorkout(
          programId: program.id!,
          name: 'Full Body Workout ${i + 1}',
          description: 'Complete full body training session',
          dayOfWeek: workoutDays[i],
          weekNumber: week,
          orderIndex: i,
        );

        final workoutId = await _databaseService.insertProgramWorkout(workout);

        // Add exercises for full body workout
        final fullBodyExercises = [
          _findExercise(exercises, 'Push-ups'),
          _findExercise(exercises, 'Squats'),
          _findExercise(exercises, 'Pull-ups'),
          _findExercise(exercises, 'Lunges'),
          _findExercise(exercises, 'Plank Pose'),
        ].where((e) => e != null).cast<Exercise>().toList();

        for (int j = 0; j < fullBodyExercises.length; j++) {
          final exercise = fullBodyExercises[j];
          final workoutExercise = WorkoutExercise(
            workoutId: workoutId,
            exerciseId: exercise.id!,
            orderIndex: j,
            sets: exercise.defaultSets,
            reps: exercise.defaultReps,
            weight: exercise.defaultWeight,
            duration: exercise.defaultDuration,
            restTime: exercise.restTime,
          );
          await _databaseService.insertWorkoutExercise(workoutExercise);
        }
      }
    }
  }

  Future<void> _createUpperLowerTemplate(WorkoutProgram program, List<Exercise> exercises) async {
    if (program.id == null) return;

    for (int week = 1; week <= program.durationWeeks; week++) {
      // Upper body workouts (Monday, Thursday)
      final upperDays = [1, 4];
      for (int i = 0; i < upperDays.length; i++) {
        final workout = ProgramWorkout(
          programId: program.id!,
          name: 'Upper Body Workout',
          description: 'Focus on chest, back, shoulders, and arms',
          dayOfWeek: upperDays[i],
          weekNumber: week,
          orderIndex: i * 2,
        );

        final workoutId = await _databaseService.insertProgramWorkout(workout);

        final upperExercises = [
          _findExercise(exercises, 'Push-ups'),
          _findExercise(exercises, 'Pull-ups'),
          _findExercise(exercises, 'Overhead Press'),
          _findExercise(exercises, 'Bent-over Rows'),
          _findExercise(exercises, 'Bicep Curls'),
        ].where((e) => e != null).cast<Exercise>().toList();

        for (int j = 0; j < upperExercises.length; j++) {
          final exercise = upperExercises[j];
          final workoutExercise = WorkoutExercise(
            workoutId: workoutId,
            exerciseId: exercise.id!,
            orderIndex: j,
            sets: exercise.defaultSets,
            reps: exercise.defaultReps,
            weight: exercise.defaultWeight,
            restTime: exercise.restTime,
          );
          await _databaseService.insertWorkoutExercise(workoutExercise);
        }
      }

      // Lower body workouts (Tuesday, Friday)
      final lowerDays = [2, 5];
      for (int i = 0; i < lowerDays.length; i++) {
        final workout = ProgramWorkout(
          programId: program.id!,
          name: 'Lower Body Workout',
          description: 'Focus on legs, glutes, and core',
          dayOfWeek: lowerDays[i],
          weekNumber: week,
          orderIndex: i * 2 + 1,
        );

        final workoutId = await _databaseService.insertProgramWorkout(workout);

        final lowerExercises = [
          _findExercise(exercises, 'Squats'),
          _findExercise(exercises, 'Lunges'),
          _findExercise(exercises, 'Deadlifts'),
          _findExercise(exercises, 'Bulgarian Split Squats'),
          _findExercise(exercises, 'Calf Raises'),
        ].where((e) => e != null).cast<Exercise>().toList();

        for (int j = 0; j < lowerExercises.length; j++) {
          final exercise = lowerExercises[j];
          final workoutExercise = WorkoutExercise(
            workoutId: workoutId,
            exerciseId: exercise.id!,
            orderIndex: j,
            sets: exercise.defaultSets,
            reps: exercise.defaultReps,
            weight: exercise.defaultWeight,
            restTime: exercise.restTime,
          );
          await _databaseService.insertWorkoutExercise(workoutExercise);
        }
      }
    }
  }

  Future<void> _createCardioTemplate(WorkoutProgram program, List<Exercise> exercises) async {
    if (program.id == null) return;

    for (int week = 1; week <= program.durationWeeks; week++) {
      final workoutDays = [1, 2, 4, 5]; // Monday, Tuesday, Thursday, Friday
      
      for (int i = 0; i < workoutDays.length && i < program.workoutsPerWeek; i++) {
        final workout = ProgramWorkout(
          programId: program.id!,
          name: 'Cardio Session ${i + 1}',
          description: 'Cardiovascular endurance training',
          dayOfWeek: workoutDays[i],
          weekNumber: week,
          orderIndex: i,
        );

        final workoutId = await _databaseService.insertProgramWorkout(workout);

        final cardioExercises = [
          _findExercise(exercises, 'Running'),
          _findExercise(exercises, 'Jump Rope'),
          _findExercise(exercises, 'Cycling'),
        ].where((e) => e != null).cast<Exercise>().toList();

        for (int j = 0; j < cardioExercises.length && j < 2; j++) {
          final exercise = cardioExercises[j];
          final workoutExercise = WorkoutExercise(
            workoutId: workoutId,
            exerciseId: exercise.id!,
            orderIndex: j,
            sets: exercise.defaultSets,
            reps: exercise.defaultReps,
            duration: exercise.defaultDuration,
            restTime: exercise.restTime,
          );
          await _databaseService.insertWorkoutExercise(workoutExercise);
        }
      }
    }
  }

  Future<void> _createHIITTemplate(WorkoutProgram program, List<Exercise> exercises) async {
    if (program.id == null) return;

    for (int week = 1; week <= program.durationWeeks; week++) {
      final workoutDays = [1, 3, 5]; // Monday, Wednesday, Friday
      
      for (int i = 0; i < workoutDays.length && i < program.workoutsPerWeek; i++) {
        final workout = ProgramWorkout(
          programId: program.id!,
          name: 'HIIT Session ${i + 1}',
          description: 'High-intensity interval training',
          dayOfWeek: workoutDays[i],
          weekNumber: week,
          orderIndex: i,
        );

        final workoutId = await _databaseService.insertProgramWorkout(workout);

        final hiitExercises = [
          _findExercise(exercises, 'Burpees'),
          _findExercise(exercises, 'Mountain Climbers'),
          _findExercise(exercises, 'Jump Squats'),
          _findExercise(exercises, 'High Knees'),
        ].where((e) => e != null).cast<Exercise>().toList();

        for (int j = 0; j < hiitExercises.length; j++) {
          final exercise = hiitExercises[j];
          final workoutExercise = WorkoutExercise(
            workoutId: workoutId,
            exerciseId: exercise.id!,
            orderIndex: j,
            sets: exercise.defaultSets,
            reps: exercise.defaultReps,
            duration: exercise.defaultDuration,
            restTime: exercise.restTime,
          );
          await _databaseService.insertWorkoutExercise(workoutExercise);
        }
      }
    }
  }

  Future<void> _createYogaTemplate(WorkoutProgram program, List<Exercise> exercises) async {
    if (program.id == null) return;

    for (int week = 1; week <= program.durationWeeks; week++) {
      final workoutDays = [1, 3, 5, 7]; // Monday, Wednesday, Friday, Sunday
      
      for (int i = 0; i < workoutDays.length && i < program.workoutsPerWeek; i++) {
        final workout = ProgramWorkout(
          programId: program.id!,
          name: 'Yoga Flow ${i + 1}',
          description: 'Mindful movement and flexibility',
          dayOfWeek: workoutDays[i],
          weekNumber: week,
          orderIndex: i,
        );

        final workoutId = await _databaseService.insertProgramWorkout(workout);

        final yogaExercises = [
          _findExercise(exercises, 'Downward Dog'),
          _findExercise(exercises, 'Warrior I'),
          _findExercise(exercises, 'Plank Pose'),
          _findExercise(exercises, 'Child\'s Pose'),
        ].where((e) => e != null).cast<Exercise>().toList();

        for (int j = 0; j < yogaExercises.length; j++) {
          final exercise = yogaExercises[j];
          final workoutExercise = WorkoutExercise(
            workoutId: workoutId,
            exerciseId: exercise.id!,
            orderIndex: j,
            sets: exercise.defaultSets,
            reps: exercise.defaultReps,
            duration: exercise.defaultDuration,
            restTime: exercise.restTime,
          );
          await _databaseService.insertWorkoutExercise(workoutExercise);
        }
      }
    }
  }

  // Simplified templates for other types
  Future<void> _createPushPullLegsTemplate(WorkoutProgram program, List<Exercise> exercises) async {
    await _createFullBodyTemplate(program, exercises); // Simplified for now
  }

  Future<void> _createStrengthTemplate(WorkoutProgram program, List<Exercise> exercises) async {
    await _createFullBodyTemplate(program, exercises); // Simplified for now
  }

  Future<void> _createFlexibilityTemplate(WorkoutProgram program, List<Exercise> exercises) async {
    await _createYogaTemplate(program, exercises); // Simplified for now
  }

  Exercise? _findExercise(List<Exercise> exercises, String name) {
    try {
      return exercises.firstWhere((exercise) => exercise.name == name);
    } catch (e) {
      return null;
    }
  }
}
