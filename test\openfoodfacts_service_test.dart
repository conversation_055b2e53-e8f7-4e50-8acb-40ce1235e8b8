import 'package:flutter_test/flutter_test.dart';
import 'package:nonni_health_journey/services/openfoodfacts_service.dart';

void main() {
  group('OpenFoodFactsService Tests', () {
    late OpenFoodFactsService service;

    setUp(() {
      service = OpenFoodFactsService();
    });

    test('should fetch product by barcode successfully', () async {
      // Test with a known Dutch product barcode (Coca-Cola)
      const testBarcode = '5449000000996';
      
      final product = await service.getProductByBarcode(testBarcode);
      
      expect(product, isNotNull);
      expect(product!.barcode, equals(testBarcode));
      expect(product.name, isNotEmpty);
      expect(product.caloriesPer100g, greaterThan(0));
      
      print('Product found: ${product.displayName}');
      print('Calories per 100g: ${product.caloriesPer100g}');
      print('Nutrition: ${product.nutritionSummary}');
    }, timeout: const Timeout(Duration(seconds: 30)));

    test('should return null for invalid barcode', () async {
      const invalidBarcode = '0000000000000';
      
      final product = await service.getProductByBarcode(invalidBarcode);
      
      expect(product, isNull);
    }, timeout: const Timeout(Duration(seconds: 30)));

    test('should search products successfully', () async {
      const searchQuery = 'coca cola';
      
      final products = await service.searchProducts(searchQuery);
      
      expect(products, isNotEmpty);
      expect(products.length, greaterThan(0));
      
      final firstProduct = products.first;
      expect(firstProduct.name, isNotEmpty);
      expect(firstProduct.barcode, isNotEmpty);
      
      print('Search results: ${products.length} products found');
      print('First product: ${firstProduct.displayName}');
    }, timeout: const Timeout(Duration(seconds: 30)));

    test('should fetch popular Dutch products', () async {
      final products = await service.getPopularDutchProducts();
      
      expect(products, isNotEmpty);
      expect(products.length, greaterThan(0));
      expect(products.length, lessThanOrEqualTo(20));
      
      final firstProduct = products.first;
      expect(firstProduct.name, isNotEmpty);
      expect(firstProduct.caloriesPer100g, greaterThan(0));
      
      print('Popular Dutch products: ${products.length} found');
      print('First product: ${firstProduct.displayName}');
    }, timeout: const Timeout(Duration(seconds: 30)));
  });
}
