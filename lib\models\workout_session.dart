import 'workout_program.dart';
import 'exercise_log.dart';

enum SessionStatus {
  planned,
  inProgress,
  completed,
  skipped,
}

class WorkoutSession {
  final int? id;
  final int? programId;
  final WorkoutProgram? program;
  final String name;
  final String description;
  final DateTime scheduledDate;
  final DateTime? startTime;
  final DateTime? endTime;
  final SessionStatus status;
  final List<ExerciseLog> exerciseLogs;
  final String notes;
  final int week;
  final int dayOfWeek;

  WorkoutSession({
    this.id,
    this.programId,
    this.program,
    required this.name,
    this.description = '',
    required this.scheduledDate,
    this.startTime,
    this.endTime,
    this.status = SessionStatus.planned,
    this.exerciseLogs = const [],
    this.notes = '',
    this.week = 1,
    this.dayOfWeek = 1,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'program_id': programId,
      'name': name,
      'description': description,
      'scheduled_date': scheduledDate.millisecondsSinceEpoch,
      'start_time': startTime?.millisecondsSinceEpoch,
      'end_time': endTime?.millisecondsSinceEpoch,
      'status': status.name,
      'notes': notes,
      'week': week,
      'day_of_week': dayOfWeek,
    };
  }

  factory WorkoutSession.fromMap(Map<String, dynamic> map) {
    return WorkoutSession(
      id: map['id'],
      programId: map['program_id'],
      name: map['name'],
      description: map['description'] ?? '',
      scheduledDate: DateTime.fromMillisecondsSinceEpoch(map['scheduled_date']),
      startTime: map['start_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['start_time'])
          : null,
      endTime: map['end_time'] != null
          ? DateTime.fromMillisecondsSinceEpoch(map['end_time'])
          : null,
      status: SessionStatus.values.firstWhere(
        (s) => s.name == map['status'],
        orElse: () => SessionStatus.planned,
      ),
      notes: map['notes'] ?? '',
      week: map['week'] ?? 1,
      dayOfWeek: map['day_of_week'] ?? 1,
    );
  }

  WorkoutSession copyWith({
    int? id,
    int? programId,
    WorkoutProgram? program,
    String? name,
    String? description,
    DateTime? scheduledDate,
    DateTime? startTime,
    DateTime? endTime,
    SessionStatus? status,
    List<ExerciseLog>? exerciseLogs,
    String? notes,
    int? week,
    int? dayOfWeek,
  }) {
    return WorkoutSession(
      id: id ?? this.id,
      programId: programId ?? this.programId,
      program: program ?? this.program,
      name: name ?? this.name,
      description: description ?? this.description,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      status: status ?? this.status,
      exerciseLogs: exerciseLogs ?? this.exerciseLogs,
      notes: notes ?? this.notes,
      week: week ?? this.week,
      dayOfWeek: dayOfWeek ?? this.dayOfWeek,
    );
  }

  Duration? get duration {
    if (startTime != null && endTime != null) {
      return endTime!.difference(startTime!);
    }
    return null;
  }

  String get durationDisplay {
    final dur = duration;
    if (dur == null) return 'Not completed';
    
    final hours = dur.inHours;
    final minutes = dur.inMinutes % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }

  int get totalExercises => exerciseLogs.length;

  int get completedExercises => exerciseLogs.where((log) => log.completed).length;

  double get completionPercentage {
    if (totalExercises == 0) return 0.0;
    return (completedExercises / totalExercises) * 100;
  }

  double get totalVolume => exerciseLogs.fold(0.0, (sum, log) => sum + log.totalVolume);

  int get totalSets => exerciseLogs.fold(0, (sum, log) => sum + log.totalSets);

  String get statusDisplay {
    switch (status) {
      case SessionStatus.planned:
        return 'Planned';
      case SessionStatus.inProgress:
        return 'In Progress';
      case SessionStatus.completed:
        return 'Completed';
      case SessionStatus.skipped:
        return 'Skipped';
    }
  }

  bool get canStart => status == SessionStatus.planned;
  bool get canComplete => status == SessionStatus.inProgress;
  bool get isActive => status == SessionStatus.inProgress;
  bool get isDone => status == SessionStatus.completed || status == SessionStatus.skipped;
}
