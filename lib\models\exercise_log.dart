import 'exercise.dart';
import 'exercise_set.dart';

class ExerciseLog {
  final int? id;
  final int workoutSessionId;
  final int exerciseId;
  final Exercise? exercise; // Populated when joining with exercise table
  final List<ExerciseSet> sets;
  final String notes;
  final DateTime startTime;
  final DateTime? endTime;
  final bool completed;

  ExerciseLog({
    this.id,
    required this.workoutSessionId,
    required this.exerciseId,
    this.exercise,
    this.sets = const [],
    this.notes = '',
    required this.startTime,
    this.endTime,
    this.completed = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workout_session_id': workoutSessionId,
      'exercise_id': exerciseId,
      'notes': notes,
      'start_time': startTime.millisecondsSinceEpoch,
      'end_time': endTime?.millisecondsSinceEpoch,
      'completed': completed ? 1 : 0,
    };
  }

  factory ExerciseLog.fromMap(Map<String, dynamic> map) {
    return ExerciseLog(
      id: map['id'],
      workoutSessionId: map['workout_session_id'],
      exerciseId: map['exercise_id'],
      notes: map['notes'] ?? '',
      startTime: DateTime.fromMillisecondsSinceEpoch(map['start_time']),
      endTime: map['end_time'] != null 
          ? DateTime.fromMillisecondsSinceEpoch(map['end_time'])
          : null,
      completed: map['completed'] == 1,
    );
  }

  ExerciseLog copyWith({
    int? id,
    int? workoutSessionId,
    int? exerciseId,
    Exercise? exercise,
    List<ExerciseSet>? sets,
    String? notes,
    DateTime? startTime,
    DateTime? endTime,
    bool? completed,
  }) {
    return ExerciseLog(
      id: id ?? this.id,
      workoutSessionId: workoutSessionId ?? this.workoutSessionId,
      exerciseId: exerciseId ?? this.exerciseId,
      exercise: exercise ?? this.exercise,
      sets: sets ?? this.sets,
      notes: notes ?? this.notes,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      completed: completed ?? this.completed,
    );
  }

  Duration? get duration {
    if (endTime != null) {
      return endTime!.difference(startTime);
    }
    return null;
  }

  int get totalSets => sets.length;

  int get completedSets => sets.where((set) => set.completed).length;

  double get totalVolume => sets.fold(0.0, (sum, set) => sum + set.volume);

  int get totalReps => sets.fold(0, (sum, set) => sum + set.reps);

  double get averageWeight {
    final weightSets = sets.where((set) => set.weight > 0);
    if (weightSets.isEmpty) return 0.0;
    return weightSets.fold(0.0, (sum, set) => sum + set.weight) / weightSets.length;
  }

  double get maxWeight {
    if (sets.isEmpty) return 0.0;
    return sets.map((set) => set.weight).reduce((a, b) => a > b ? a : b);
  }

  String get progressSummary {
    if (sets.isEmpty) return 'No sets completed';
    
    if (exercise?.type == ExerciseType.cardio) {
      final totalDuration = sets.fold(0, (sum, set) => sum + set.duration);
      final minutes = totalDuration ~/ 60;
      final seconds = totalDuration % 60;
      return '$totalSets sets, ${minutes}m ${seconds}s total';
    } else {
      final maxWeightStr = maxWeight > 0 ? ' @ ${maxWeight.toStringAsFixed(1)}kg' : '';
      return '$totalSets sets, $totalReps reps$maxWeightStr';
    }
  }

  bool get isInProgress => !completed && sets.isNotEmpty;
}
