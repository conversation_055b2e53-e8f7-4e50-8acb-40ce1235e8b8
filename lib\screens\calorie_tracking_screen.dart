import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/food_entry.dart';
import '../models/nutrition_recommendation.dart';
import '../models/workout_program.dart';
import '../models/user_active_program.dart';
import '../services/database_service.dart';
import '../services/nutrition_service.dart';

class CalorieTrackingScreen extends StatefulWidget {
  const CalorieTrackingScreen({super.key});

  @override
  State<CalorieTrackingScreen> createState() => _CalorieTrackingScreenState();
}

class _CalorieTrackingScreenState extends State<CalorieTrackingScreen> {
  int _dailyCalorieGoal = 2000;
  int _consumedCalories = 0;
  List<FoodEntry> _foodEntries = [];
  final DatabaseService _databaseService = DatabaseService();
  DateTime _selectedDate = DateTime.now();

  // Program-based nutrition state
  bool _isProgramBasedNutritionEnabled = false;
  NutritionRecommendation? _currentRecommendation;
  WorkoutProgram? _activeProgram;
  UserActiveProgram? _activeUserProgram;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    final goal = await _databaseService.getDailyCalorieGoal();
    final entries = await _databaseService.getFoodEntriesForDate(_selectedDate);

    // Load program-based nutrition data
    final isProgramBasedEnabled = await _databaseService.isProgramBasedNutritionEnabled();
    final activeUserProgram = await _databaseService.getCurrentActiveProgram();
    WorkoutProgram? activeProgram;
    NutritionRecommendation? recommendation;

    if (isProgramBasedEnabled && activeUserProgram != null) {
      activeProgram = await _databaseService.getWorkoutProgramById(activeUserProgram.programId);
      if (activeProgram != null) {
        // Get user's nutrition goal preference
        final goalSetting = await _databaseService.getNutritionGoal();
        NutritionGoal? userGoal;
        if (goalSetting != null) {
          userGoal = NutritionGoal.values.firstWhere(
            (g) => g.name == goalSetting,
            orElse: () => NutritionGoal.maintenance,
          );
        }

        recommendation = NutritionService.calculateRecommendation(
          program: activeProgram,
          userGoal: userGoal,
        );

        // Save the recommendation for future reference
        await _databaseService.saveNutritionRecommendation(recommendation.toMap());
      }
    }

    setState(() {
      _dailyCalorieGoal = isProgramBasedEnabled && recommendation != null
          ? recommendation.dailyCalories
          : goal;
      _foodEntries = entries;
      _consumedCalories = entries.fold(0, (sum, entry) => sum + entry.calories);
      _isProgramBasedNutritionEnabled = isProgramBasedEnabled;
      _currentRecommendation = recommendation;
      _activeProgram = activeProgram;
      _activeUserProgram = activeUserProgram;
    });
  }

  Future<void> _addFoodEntry() async {
    showDialog(
      context: context,
      builder: (context) => _AddFoodDialog(
        onAdd: (String food, int calories) async {
          final entry = FoodEntry(
            food: food,
            calories: calories,
            time: DateTime.now(),
          );

          await _databaseService.insertFoodEntry(entry);
          await _loadData(); // Reload data to update UI
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final remainingCalories = _dailyCalorieGoal - _consumedCalories;
    final progress = _consumedCalories / _dailyCalorieGoal;

    return Scaffold(
      appBar: AppBar(
        title: const Text("Nonni's Nutrition Tracker"),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: _showSettingsDialog,
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            // Daily Summary Card
            Container(
              margin: const EdgeInsets.all(16),
              child: Card(
                elevation: 6,
                shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
                child: Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(20),
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                        Theme.of(context).colorScheme.secondary.withValues(alpha: 0.1),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(20.0),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.favorite,
                              color: Theme.of(context).colorScheme.tertiary,
                              size: 24,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              "Nonni's Daily Progress",
                              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          DateFormat('EEEE, MMM dd, yyyy').format(DateTime.now()),
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 20),
                        Container(
                          height: 8,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4),
                            color: Colors.grey[300],
                          ),
                          child: FractionallySizedBox(
                            alignment: Alignment.centerLeft,
                            widthFactor: progress.clamp(0.0, 1.0),
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(4),
                                gradient: LinearGradient(
                                  colors: progress > 1.0
                                    ? [Colors.red[400]!, Colors.red[600]!]
                                    : [Theme.of(context).colorScheme.primary, Theme.of(context).colorScheme.secondary],
                                ),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 20),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            _buildCalorieInfo('Consumed', _consumedCalories, Theme.of(context).colorScheme.secondary),
                            _buildCalorieInfo('Remaining', remainingCalories,
                              remainingCalories >= 0 ? Theme.of(context).colorScheme.primary : Colors.red),
                            _buildCalorieInfo('Goal', _dailyCalorieGoal, Colors.blue[600]!),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            // Program-based nutrition banner
            if (_isProgramBasedNutritionEnabled && _currentRecommendation != null)
              _buildNutritionBanner(),
            const SizedBox(height: 16),
            // Food Entries List
            Expanded(
              child: _foodEntries.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.restaurant_menu,
                            size: 80,
                            color: Colors.grey[300],
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Hey Nonni! 👋',
                            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                              color: Theme.of(context).colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Ready to start tracking your nutrition?\nTap the "Add Food" button to log your first meal!',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey[600],
                              height: 1.4,
                            ),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _foodEntries.length,
                      itemBuilder: (context, index) {
                        final entry = _foodEntries[index];
                        return Card(
                          elevation: 2,
                          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                          child: ListTile(
                            leading: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.secondary.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                Icons.restaurant_menu,
                                color: Theme.of(context).colorScheme.secondary,
                              ),
                            ),
                            title: Text(
                              entry.food,
                              style: const TextStyle(fontWeight: FontWeight.w600),
                            ),
                            subtitle: Text(
                              DateFormat('HH:mm').format(entry.time),
                              style: TextStyle(color: Colors.grey[600]),
                            ),
                            trailing: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: Theme.of(context).colorScheme.primary.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(20),
                              ),
                              child: Text(
                                '${entry.calories} cal',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                              ),
                            ),
                          ),
                        );
                      },
                    ),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addFoodEntry,
        icon: const Icon(Icons.add),
        label: const Text('Add Food'),
        backgroundColor: Theme.of(context).colorScheme.secondary,
        foregroundColor: Colors.white,
      ),
    );
  }

  void _showSettingsDialog() {
    final goalController = TextEditingController(text: _dailyCalorieGoal.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text("Nonni's Daily Goal"),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Set your daily calorie goal:'),
            const SizedBox(height: 16),
            TextField(
              controller: goalController,
              decoration: const InputDecoration(
                labelText: 'Daily Calorie Goal',
                suffixText: 'calories',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.number,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final newGoal = int.tryParse(goalController.text) ?? _dailyCalorieGoal;
              await _databaseService.setDailyCalorieGoal(newGoal);
              setState(() {
                _dailyCalorieGoal = newGoal;
              });
              Navigator.of(context).pop();
            },
            child: const Text('Save'),
          ),
        ],
      ),
    );
  }

  Widget _buildCalorieInfo(String label, int value, Color color) {
    return Column(
      children: [
        Text(
          value.toString(),
          style: TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12, color: Colors.grey),
        ),
      ],
    );
  }

  Widget _buildNutritionBanner() {
    if (_currentRecommendation == null || _activeProgram == null) {
      return const SizedBox.shrink();
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Card(
        elevation: 4,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.1),
                Theme.of(context).colorScheme.tertiary.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.fitness_center,
                      color: Theme.of(context).colorScheme.tertiary,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Program-Based Nutrition',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.tertiary,
                        ),
                      ),
                    ),
                    IconButton(
                      icon: const Icon(Icons.info_outline),
                      onPressed: _showNutritionDetails,
                      color: Theme.of(context).colorScheme.tertiary,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  'Optimized for ${_activeProgram!.name}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  NutritionService.getQuickSummary(_currentRecommendation!),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildMacroIndicator(
                        'Protein',
                        _currentRecommendation!.proteinGrams.round(),
                        _currentRecommendation!.proteinPercentage,
                        Colors.red,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildMacroIndicator(
                        'Carbs',
                        _currentRecommendation!.carbsGrams.round(),
                        _currentRecommendation!.carbsPercentage,
                        Colors.blue,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildMacroIndicator(
                        'Fat',
                        _currentRecommendation!.fatGrams.round(),
                        _currentRecommendation!.fatPercentage,
                        Colors.orange,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMacroIndicator(String label, int grams, double percentage, Color color) {
    return Column(
      children: [
        Text(
          '${grams}g',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 10, color: Colors.grey),
        ),
        Text(
          '${percentage.round()}%',
          style: const TextStyle(fontSize: 10, color: Colors.grey),
        ),
      ],
    );
  }

  void _showNutritionDetails() {
    if (_currentRecommendation == null || _activeProgram == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
        title: Row(
          children: [
            Icon(
              Icons.fitness_center,
              color: Theme.of(context).colorScheme.tertiary,
            ),
            const SizedBox(width: 8),
            const Text('Nutrition Details'),
          ],
        ),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Program: ${_activeProgram!.name}',
                style: const TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(_currentRecommendation!.reasoning),
              const SizedBox(height: 16),
              const Text(
                'Nutrition Tips:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ..._currentRecommendation!.tips.map((tip) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• '),
                    Expanded(child: Text(tip)),
                  ],
                ),
              )),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showNutritionSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.tertiary,
              foregroundColor: Colors.white,
            ),
            child: const Text('Settings'),
          ),
        ],
      ),
    );
  }

  void _showNutritionSettings() {
    // TODO: Implement nutrition settings dialog
    // This would allow users to:
    // - Enable/disable program-based nutrition
    // - Set their nutrition goal (weight loss, muscle gain, etc.)
    // - Override recommendations manually
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Nutrition settings coming soon!'),
      ),
    );
  }
}

class _AddFoodDialog extends StatefulWidget {
  final Function(String, int) onAdd;

  const _AddFoodDialog({required this.onAdd});

  @override
  State<_AddFoodDialog> createState() => _AddFoodDialogState();
}

class _AddFoodDialogState extends State<_AddFoodDialog> {
  final _foodController = TextEditingController();
  final _caloriesController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      title: Row(
        children: [
          Icon(
            Icons.restaurant_menu,
            color: Theme.of(context).colorScheme.secondary,
          ),
          const SizedBox(width: 8),
          const Text('Add Food Entry'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _foodController,
            decoration: InputDecoration(
              labelText: 'Food/Meal',
              hintText: 'e.g., Chicken Salad',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.fastfood),
            ),
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _caloriesController,
            decoration: InputDecoration(
              labelText: 'Calories',
              hintText: 'e.g., 350',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              prefixIcon: const Icon(Icons.local_fire_department),
              suffixText: 'cal',
            ),
            keyboardType: TextInputType.number,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            final food = _foodController.text.trim();
            final calories = int.tryParse(_caloriesController.text) ?? 0;

            if (food.isNotEmpty && calories > 0) {
              widget.onAdd(food, calories);
              Navigator.of(context).pop();
            }
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: Theme.of(context).colorScheme.secondary,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
          ),
          child: const Text('Add'),
        ),
      ],
    );
  }
}
