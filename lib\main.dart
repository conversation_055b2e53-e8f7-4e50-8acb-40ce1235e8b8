import 'package:flutter/material.dart';
import 'screens/home_screen.dart';
import 'services/database_service.dart';
import 'services/exercise_data_service.dart';
import 'services/workout_program_data_service.dart';
import 'services/program_template_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // Initialize database
  await DatabaseService().database;
  // Initialize exercise library
  await ExerciseDataService().initializeExerciseLibrary();
  // Initialize workout programs
  await WorkoutProgramDataService().initializeWorkoutPrograms();
  // Initialize program templates
  await ProgramTemplateService().initializeProgramTemplates();
  runApp(const NonniDietApp());
}

class NonniDietApp extends StatelessWidget {
  const NonniDietApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: "Nonni's Health Journey",
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF4CAF50), // Beautiful green
          brightness: Brightness.light,
        ).copyWith(
          primary: const Color(0xFF4CAF50),
          secondary: const Color(0xFFFF9800), // Orange accent
          tertiary: const Color(0xFFE91E63), // Pink accent
          surface: const Color(0xFFF8F9FA),
        ),
        useMaterial3: true,
        fontFamily: 'Roboto',
        appBarTheme: const AppBarTheme(
          centerTitle: true,
          elevation: 0,
          titleTextStyle: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        floatingActionButtonTheme: const FloatingActionButtonThemeData(
          elevation: 6,
          shape: CircleBorder(),
        ),
      ),
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
