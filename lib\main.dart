import 'package:flutter/material.dart';
import 'screens/home_screen.dart';
import 'services/database_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  // Initialize database
  await DatabaseService().database;
  runApp(const NonniDietApp());
}

class NonniDietApp extends StatelessWidget {
  const NonniDietApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: "Nonni's Health Journey",
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(
          seedColor: const Color(0xFF7B68EE), // Beautiful purple for Nonni
          brightness: Brightness.light,
        ).copyWith(
          primary: const Color(0xFF7B68EE), // Medium slate blue
          secondary: const Color(0xFFBA68C8), // Medium orchid
          tertiary: const Color(0xFF9C27B0), // Purple accent
          surface: const Color(0xFFF8F6FF), // Very light purple tint
        ),
        useMaterial3: true,
        fontFamily: 'Roboto',
        appBarTheme: const AppBarTheme(
          centerTitle: true,
          elevation: 0,
          titleTextStyle: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w600,
            color: Colors.white,
          ),
        ),
        cardTheme: const CardThemeData(
          elevation: 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.all(Radius.circular(16)),
          ),
          margin: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        ),
        floatingActionButtonTheme: const FloatingActionButtonThemeData(
          elevation: 6,
          shape: CircleBorder(),
        ),
      ),
      home: const HomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}
