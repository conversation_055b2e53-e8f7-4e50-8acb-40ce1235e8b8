enum ExerciseType {
  strength,
  cardio,
  flexibility,
  balance,
  hiit,
}

enum MuscleGroup {
  chest,
  back,
  shoulders,
  biceps,
  triceps,
  legs,
  glutes,
  core,
  cardio,
  fullBody,
}

enum Equipment {
  none, // bodyweight
  dumbbells,
  barbell,
  kettlebell,
  resistanceBand,
  machine,
  cable,
  cableMachine,
  pullUpBar,
  other,
}

class Exercise {
  final int? id;
  final String name;
  final String description;
  final ExerciseType type;
  final List<MuscleGroup> muscleGroups;
  final Equipment equipment;
  final String instructions;
  final String imageUrl;
  final int defaultSets;
  final int defaultReps;
  final double defaultWeight;
  final int defaultDuration; // in seconds for cardio
  final int restTime; // in seconds

  Exercise({
    this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.muscleGroups,
    required this.equipment,
    required this.instructions,
    this.imageUrl = '',
    this.defaultSets = 3,
    this.defaultReps = 10,
    this.defaultWeight = 0.0,
    this.defaultDuration = 0,
    this.restTime = 60,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.name,
      'muscle_groups': muscleGroups.map((mg) => mg.name).join(','),
      'equipment': equipment.name,
      'instructions': instructions,
      'image_url': imageUrl,
      'default_sets': defaultSets,
      'default_reps': defaultReps,
      'default_weight': defaultWeight,
      'default_duration': defaultDuration,
      'rest_time': restTime,
    };
  }

  factory Exercise.fromMap(Map<String, dynamic> map) {
    return Exercise(
      id: map['id'],
      name: map['name'],
      description: map['description'],
      type: ExerciseType.values.firstWhere(
        (e) => e.name == map['type'],
        orElse: () => ExerciseType.strength,
      ),
      muscleGroups: (map['muscle_groups'] as String)
          .split(',')
          .where((mg) => mg.isNotEmpty)
          .map((mg) => MuscleGroup.values.firstWhere(
                (e) => e.name == mg,
                orElse: () => MuscleGroup.fullBody,
              ))
          .toList(),
      equipment: Equipment.values.firstWhere(
        (e) => e.name == map['equipment'],
        orElse: () => Equipment.none,
      ),
      instructions: map['instructions'],
      imageUrl: map['image_url'] ?? '',
      defaultSets: map['default_sets'] ?? 3,
      defaultReps: map['default_reps'] ?? 10,
      defaultWeight: map['default_weight']?.toDouble() ?? 0.0,
      defaultDuration: map['default_duration'] ?? 0,
      restTime: map['rest_time'] ?? 60,
    );
  }

  String get muscleGroupsDisplay {
    return muscleGroups.map((mg) => _muscleGroupDisplayName(mg)).join(', ');
  }

  String get equipmentDisplay {
    return _equipmentDisplayName(equipment);
  }

  String _muscleGroupDisplayName(MuscleGroup mg) {
    switch (mg) {
      case MuscleGroup.chest:
        return 'Chest';
      case MuscleGroup.back:
        return 'Back';
      case MuscleGroup.shoulders:
        return 'Shoulders';
      case MuscleGroup.biceps:
        return 'Biceps';
      case MuscleGroup.triceps:
        return 'Triceps';
      case MuscleGroup.legs:
        return 'Legs';
      case MuscleGroup.glutes:
        return 'Glutes';
      case MuscleGroup.core:
        return 'Core';
      case MuscleGroup.cardio:
        return 'Cardio';
      case MuscleGroup.fullBody:
        return 'Full Body';
    }
  }

  String _equipmentDisplayName(Equipment eq) {
    switch (eq) {
      case Equipment.none:
        return 'Bodyweight';
      case Equipment.dumbbells:
        return 'Dumbbells';
      case Equipment.barbell:
        return 'Barbell';
      case Equipment.kettlebell:
        return 'Kettlebell';
      case Equipment.resistanceBand:
        return 'Resistance Band';
      case Equipment.machine:
        return 'Machine';
      case Equipment.cable:
        return 'Cable';
      case Equipment.cableMachine:
        return 'Cable Machine';
      case Equipment.pullUpBar:
        return 'Pull-up Bar';
      case Equipment.other:
        return 'Other';
    }
  }
}
